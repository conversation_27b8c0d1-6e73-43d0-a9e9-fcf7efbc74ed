import { auth } from '@rie/auth';
import type { Context, Next } from 'hono';

const authMiddleware = async (ctx: Context, next: Next) => {
  // Allow OPTIONS requests to pass through
  if (ctx.req.method === 'OPTIONS') {
    return next();
  }

  const userSession = await auth.api.getSession({
    headers: ctx.req.raw.headers,
  });

  if (!userSession?.session) {
    return new Response('Unauthorized', { status: 401 });
  }

  ctx.set('session', userSession.session);
  await next();
};

export const createAuthMiddleware = () => authMiddleware;
