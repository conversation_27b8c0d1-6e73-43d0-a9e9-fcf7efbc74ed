import { handleEffectError } from '@/api/v2/utils/error-handler';
import { BuildingsRuntime } from '@/infrastructure/runtimes/buildings.runtime';
import { BuildingsServiceLive } from '@/infrastructure/services/buildings.service';
import { effectValidator } from '@hono/effect-validator';
import {
  BuildingInputSchema,
  BuildingSchema,
  ResourceIdSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

export const createBuildingRoute = describeRoute({
  description: 'Créer un bâtiment',
  operationId: 'createBuilding',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(BuildingInputSchema),
        example: {
          campusId: 'sugfz9s755ypvxe28y7xwcgf',
          sadId: 'NEW_BUILDING_001',
          translations: [
            {
              locale: 'en',
              name: 'New Science Building',
              description: 'New main science building',
            },
            {
              locale: 'fr',
              name: 'Nouveau Bâtiment des Sciences',
              description: 'Nouveau bâtiment principal des sciences',
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(BuildingSchema),
        },
      },
      description: 'Bâtiment créé',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Clé étrangère non trouvée - Le campus n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Buildings'],
});

export const updateBuildingRoute = describeRoute({
  description: 'Mettre à jour un bâtiment',
  operationId: 'updateBuilding',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du bâtiment (format CUID)',
      example: 'bw6c4rifj8239sc6gheruphi',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(BuildingInputSchema),
        example: {
          campusId: 'sugfz9s755ypvxe28y7xwcgf',
          sadId: 'UPDATED_BUILDING_001',
          translations: [
            {
              locale: 'en',
              name: 'Updated Science Building',
              description: 'Updated main science building',
            },
            {
              locale: 'fr',
              name: 'Bâtiment des Sciences Mis à Jour',
              description: 'Bâtiment principal des sciences mis à jour',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(BuildingSchema),
        },
      },
      description: 'Bâtiment mis à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Bâtiment non trouvé ou le campus n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Buildings'],
});

export const deleteBuildingRoute = describeRoute({
  description: 'Supprimer un bâtiment',
  operationId: 'deleteBuilding',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du bâtiment (format CUID)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Bâtiment supprimé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Bâtiment non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Buildings'],
});

export const getBuildingByIdRoute = describeRoute({
  description: 'Obtenir un bâtiment par ID',
  operationId: 'getBuildingById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du bâtiment (format CUID)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(BuildingSchema),
        },
      },
      description: 'Bâtiment trouvé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Bâtiment non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Buildings'],
});

export const getAllBuildingsRoute = describeRoute({
  description: 'Lister tous les bâtiments',
  operationId: 'getAllBuildings',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(BuildingSchema)),
        },
      },
      description: 'Bâtiments retournés',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Buildings'],
});

const buildingsRoute = new Hono();

buildingsRoute.get('/', getAllBuildingsRoute, async (ctx) => {
  const program = Effect.gen(function* () {
    const buildingService = yield* BuildingsServiceLive;
    return yield* buildingService.getAllBuildings();
  });
  const result = await BuildingsRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(result)) {
    return ctx.json(result.value);
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});

buildingsRoute.get(
  '/:id',
  getBuildingByIdRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const buildingService = yield* BuildingsServiceLive;
      return yield* buildingService.getBuildingById(id);
    });
    const result = await BuildingsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

buildingsRoute.post(
  '/',
  createBuildingRoute,
  effectValidator('json', BuildingInputSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
      const buildingService = yield* BuildingsServiceLive;
      return yield* buildingService.createBuilding(body);
    });
    const result = await BuildingsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

buildingsRoute.put(
  '/:id',
  updateBuildingRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  effectValidator('json', BuildingInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
      const buildingService = yield* BuildingsServiceLive;
      return yield* buildingService.updateBuilding({ id, ...body });
    });
    const result = await BuildingsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

buildingsRoute.delete(
  '/:id',
  deleteBuildingRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const buildingService = yield* BuildingsServiceLive;
      return yield* buildingService.deleteBuilding(id);
    });
    const result = await BuildingsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Building deleted' });
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { buildingsRoute };
