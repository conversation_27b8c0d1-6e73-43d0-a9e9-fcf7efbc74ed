COMPOSE_PROJECT_NAME=workspaces

# # echo `ifconfig | grep -v '127.0.0.1' | grep 'inet ' | grep -oE 'inet [0-9]+(\.[0-9]+){3}' | cut -d' ' -f2 | head -n 1`
# LOCAL_IP=${LOCAL_IP:-localhost}
# # use external DNS or IP address

SCHEME=http
HOST=localhost
PORT=3000
NEXT_PUBLIC_BASE_PATH=/rie

# node env
PORT=${PORT}
NEXT_PUBLIC_BASE_PATH=${NEXT_PUBLIC_BASE_PATH}
# apps/web-app/env.ts
NEXT_PUBLIC_ORIGIN_URL=${NEXT_SCHEME}://${NEXT_HOST}:${PORT}
NEXT_PUBLIC_RIE_API_URL=https://rie-devel.cen.umontreal.ca/api/
NEXT_PUBLIC_RIE_AUTH_URL=https://rie-devel.cen.umontreal.ca/api/auth
RIE_AUTH_CLIENT_ID=testclient
RIE_AUTH_CLIENT_SECRET=testpass

# NEXT_PUBLIC_RIE_API_URL=http://localhost:8009/api/
# NEXT_PUBLIC_RIE_AUTH_URL=http://localhost:8009/api/auth
# RIE_AUTH_CLIENT_ID=testclient
# RIE_AUTH_CLIENT_SECRET=testpass