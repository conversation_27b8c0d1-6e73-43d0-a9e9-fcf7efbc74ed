import { effectValidator } from '@hono/effect-validator';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

import { handleEffectError } from '@/api/v2/utils/error-handler';
import { VendorsRuntime } from '@/infrastructure/runtimes/vendors.runtime';
import { VendorsServiceLive } from '@/infrastructure/services/vendors.service';
import {
  CreateVendorSchema,
  ResourceIdSchema,
  UpdateVendorSchema,
  VendorResponseSchema,
} from '@rie/domain/schemas';

export const createVendorRoute = describeRoute({
  description: 'Créer un manufacturier',
  operationId: 'createVendor',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(CreateVendorSchema),
        example: {
          name: 'Acme Corporation',
          translations: [
            {
              locale: 'en',
              name: 'Acme Corporation',
              description: 'Leading manufacturer of laboratory equipment',
            },
            {
              locale: 'fr',
              name: 'Corporation Acme',
              description: "Fabricant leader d'équipements de laboratoire",
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(VendorResponseSchema),
        },
      },
      description: 'Manufacturier créé avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturier non trouvé',
    },
    409: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturier déjà existant',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Vendors'],
});

export const updateVendorRoute = describeRoute({
  description: 'Mettre à jour un manufacturier',
  operationId: 'updateVendor',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du manufacturier à mettre à jour',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(UpdateVendorSchema),
        example: {
          name: 'Updated Acme Corporation',
          translations: [
            {
              locale: 'en',
              name: 'Updated Acme Corporation',
              description:
                'Updated leading manufacturer of laboratory equipment',
            },
            {
              locale: 'fr',
              name: 'Corporation Acme Mise à Jour',
              description:
                "Fabricant leader mis à jour d'équipements de laboratoire",
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(VendorResponseSchema),
        },
      },
      description: 'Manufacturier mis à jour avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Vendeur non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Vendors'],
});

export const deleteVendorRoute = describeRoute({
  description: 'Supprimer un manufacturier',
  operationId: 'deleteVendor',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du manufacturier à supprimer',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              success: Schema.Boolean,
              message: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturier supprimé avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturier non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Vendors'],
});

export const getAllVendorsRoute = describeRoute({
  description: 'Lister tous les manufacturiers',
  operationId: 'getAllVendors',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(VendorResponseSchema)),
        },
      },
      description: 'Manufacturiers retournés avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturiers non trouvés',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Vendors'],
});

export const getVendorByIdRoute = describeRoute({
  description: 'Obtenir un manufacturier par ID',
  operationId: 'getVendorById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du manufacturier à récupérer',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(VendorResponseSchema),
        },
      },
      description: 'Manufacturier retourné avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturier non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Vendors'],
});

const vendorsRoute = new Hono();

vendorsRoute.get('/', getAllVendorsRoute, async (ctx) => {
  const program = Effect.gen(function* () {
    const vendorService = yield* VendorsServiceLive;
    return yield* vendorService.getAllVendors();
  });

  const vendors = await VendorsRuntime.runPromise(program);

  return ctx.json(vendors);
});

vendorsRoute.post(
  '/',
  createVendorRoute,
  effectValidator('json', CreateVendorSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const vendorService = yield* VendorsServiceLive;
      return yield* vendorService.createVendor(body);
    });

    const maybeVendor = await VendorsRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, maybeVendor);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(maybeVendor)) {
      return ctx.json(maybeVendor.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

vendorsRoute.put(
  '/:id',
  updateVendorRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  effectValidator('json', UpdateVendorSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const vendorService = yield* VendorsServiceLive;
      return yield* vendorService.updateVendor({ id, ...body });
    });

    const maybeVendor = await VendorsRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, maybeVendor);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(maybeVendor)) {
      return ctx.json(maybeVendor.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

vendorsRoute.delete(
  '/:id',
  deleteVendorRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');

    const program = Effect.gen(function* () {
      const vendorService = yield* VendorsServiceLive;
      return yield* vendorService.deleteVendor(id);
    });

    const result = await VendorsRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    return ctx.json({
      success: true,
      message: 'Vendor deleted successfully',
    });
  },
);

vendorsRoute.get(
  '/:id',
  getVendorByIdRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');

    const program = Effect.gen(function* () {
      const vendorService = yield* VendorsServiceLive;
      return yield* vendorService.getVendorById(id);
    });

    const maybeVendor = await VendorsRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, maybeVendor);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(maybeVendor)) {
      return ctx.json(maybeVendor.value);
    }

    return ctx.json({ error: 'Vendor not found' }, 404);
  },
);

export { vendorsRoute };
