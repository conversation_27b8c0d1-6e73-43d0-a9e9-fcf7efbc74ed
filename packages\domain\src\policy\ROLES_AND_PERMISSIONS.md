# Référence du Système de Permissions

Ce document décrit l'architecture du système de permissions de l'application. Il détaille les groupes de permissions (capacités techniques) et les rôles (fonctions métier) qui composent le système de contrôle d'accès.

**Principe Fondamental** : Tout groupe de permissions accordant un droit d'écriture (`create`, `update`, `delete`) inclut implicitement les droits de lecture (`read`) nécessaires. Cela garantit qu'un utilisateur peut toujours voir ce sur quoi il agit.

---

## 1. Catalogue des Groupes de Permissions

Les groupes de permissions sont des "trousseaux de clés" techniques et réutilisables. Ils répondent à la question : **"QUOI peut-on faire ?"**.

### Domaine : User (Gestion des Usagers)
| Groupe de Permission | Description                                       | Permissions Numériques Associées |
|:---------------------|:--------------------------------------------------|:---------------------------------|
| `CanViewUsers`       | Permet de voir, chercher et trier les usagers.    | `[19]`                           |
| `CanCreateUser`      | Permet de créer un nouvel usager.                 | `[6, 19]`                        |
| `CanEditUser`        | Permet de modifier un usager et de le désactiver. | `[7, 11, 19]`                    |
| `CanDeleteUser`      | Permet de supprimer un usager.                    | `[10, 19]`                       |
| `UserManagement`     | Droits de gestion complets sur les usagers.       | `[6, 7, 10, 11, 19]`             |

### Domaine : PermissionGroup (Gestion des Groupes)
| Groupe de Permission              | Description                                        | Permissions Numériques Associées |
|:----------------------------------|:---------------------------------------------------|:---------------------------------|
| `CanViewPermissionGroups`         | Permet de consulter la liste des groupes.          | `[15, 19]`                       |
| `CanManagePermissionGroupMembers` | Permet d'assigner/retirer des usagers aux groupes. | `[14]`                           |

### Domaine : Infrastructure
| Groupe de Permission       | Description                                         | Permissions Numériques Associées           |
|:---------------------------|:----------------------------------------------------|:-------------------------------------------|
| `CanViewInfrastructures`   | Permet de voir les infrastructures et d'exporter.   | `[52, 62, 78]`                             |
| `CanCreateInfrastructure`  | Permet de créer une infrastructure.                 | `[27, 52, 62, 78]`                         |
| `CanEditInfrastructure`    | Permet de modifier une infrastructure.              | `[28, 29, 31, 32, 33, 34, 52, 62, 69, 78]` |
| `CanDeleteInfrastructure`  | Permet de supprimer une infrastructure.             | `[30, 52, 62, 78]`                         |
| `InfrastructureManagement` | Droits de gestion complets sur les infrastructures. | `[27, 28, 29, 30, 31, 32, 33, 34, 69]`     |

### Domaine : Equipment
| Groupe de Permission  | Description                                                   | Permissions Numériques Associées                       |
|:----------------------|:--------------------------------------------------------------|:-------------------------------------------------------|
| `CanViewEquipment`    | Permet de voir les équipements, leurs détails, et d'exporter. | `[52, 54, 61, 64, 65, 66, 79]`                         |
| `CanCreateEquipment`  | Permet de créer un nouvel équipement.                         | `[35, 52, 54, 61, 64, 65, 66, 79]`                     |
| `CanEditEquipment`    | Permet de modifier un équipement.                             | `[36, 37, 39, 40, 41, 42, 52, 54, 61, 64, 65, 66, 79]` |
| `CanDeleteEquipment`  | Permet de supprimer un équipement.                            | `[38, 52, 54, 61, 64, 65, 66, 79]`                     |
| `EquipmentManagement` | Droits de gestion complets sur les équipements.               | `[35, 36, 37, 38, 39, 40, 41, 42]`                     |

### Domaine : Directory (Bottin) & Controlled Lists
| Groupe de Permission                  | Description                                            | Permissions Numériques Associées |
|:--------------------------------------|:-------------------------------------------------------|:---------------------------------|
| `CanAccessDirectory`                  | Permet d'avoir l'onglet "Bottin" dans le menu.         | `[17]`                           |
| `CanCreateDirectoryEntity`            | Permet de créer un objet générique du bottin.          | `[48]`                           |
| `CanEditDirectoryEntity`              | Permet de modifier et désactiver un objet du bottin.   | `[49, 51]`                       |
| `CanDeleteDirectoryEntity`            | Permet de supprimer un objet du bottin.                | `[50]`                           |
| `CanManageDirectoryEntityPermissions` | Permet de modifier la section "Permissions d'édition". | `[71]`                           |
| `ControlledListManagement`            | Droits de gestion complets sur les listes contrôlées.  | `[18, 20, 21, 22, 23, 24, 25]`   |

### Niveaux d'Accès (Visibilité des données)
| Groupe de Permission | Description                                                    | Permissions Numériques Associées |
|:---------------------|:---------------------------------------------------------------|:---------------------------------|
| `CanViewUdeMData`    | Déverrouille la lecture des données avec la visibilité "UdeM". | `[72]`                           |
| `CanViewPartnerData` | Déverrouille la lecture des données "UdeM & Partenaires".      | `[67]`                           |
| `CanViewPrivateData` | Déverrouille la lecture des données "privée".                  | `[68]`                           |

---

## 2. Catalogue des Rôles et Héritages

Les rôles représentent une fonction métier. Le système utilise des **rôles de base** (templates techniques) pour centraliser les permissions, et des **rôles spécifiques** (assignables aux utilisateurs) qui héritent de ces bases.

### Rôles de Base (Non contextuels)

| Rôle de Base  | Description                                                                        | Hérite de |
|:--------------|:-----------------------------------------------------------------------------------|:----------|
| `UdeM`        | Permet aux utilisateurs d'accéder aux données de avec la visibilité "UdeM".        | -         |
| `UdeMPartner` | Permet aux utilisateurs d'accéder aux données de avec la visibilité "UdeMPartner". | -         |
| `User`        | Permet aux utilisateurs d'accéder aux données de avec la visibilité "Public".      | -         |

*Ces rôles seront assignés automatiquement aux utilisateurs lors de la création de leur compte.*
- Si l'utilisateur se connecte à travers un compte UdeM, il recevra automatiquement le rôle `UdeM`. 
- Si l'utilisateur est créé par un gestionnaire d'établissement, il recevra le rôle `UdeMPartner`.
- Si l'utilisateur crée un compte via l'inscription publique, il recevra le rôle `User`.

### Rôles Spécifiques (Assignables aux Utilisateurs)

#### Rôles Contextuels
*Ces rôles doivent être assignés avec un contexte (`institution`, `unit`, `infrastructure`).*

**Famille "Explorer" (Lecture seule)**
- **`InstitutionExplorer`** (Hérite de: `UnitExplorer`)
- **`UnitExplorer`** (Hérite de: `InstitutionExplorer`)
- **`InfrastructureExplorer`** (Hérite de: `EquipmentExplorer`)
- **`EquipmentExplorer`** (Hérite de: -)

**Famille "Editor" (Gestion des données)**
- **`InstitutionEditor`** (Hérite de: `UnitEditor`)
- **`UnitEditor`** (Hérite de: `InstitutionEditor`)
- **`InfrastructureEditor`** (Hérite de: -)

**Famille "Manager" (Gestion des données ET des personnes/permissions)**
- **`InfrastructureManager`**
    - **Hérite de**: `EquipmentManager`
    - **Permissions Supplémentaires**: `CanManageDirectoryEntityPermissions`
- **`UnitManager`**
    - **Hérite de**: `InfrastructureManager`, `UnitEditor`
    - **Permissions Supplémentaires**: `UserManagement`, `CanManagePermissionGroupMembers`
- **`InstitutionManager`**
    - **Hérite de**: `UnitManager`, `InstitutionEditor`
    - **Permissions Supplémentaires**: `ControlledListManagement`

#### Rôles Globaux (Sans Contexte)
*Ces rôles sont assignés sans contexte et s'appliquent à toute la plateforme.*

- **`SuperExplorer`** (Hérite de: `InstitutionExplorer`, `UnitExplorer`, )
- **`SuperEditor`** (Hérite de: `BaseEditor`, `SuperExplorer`)
- **`SystemAdmin`** (Hérite de: `InstitutionManager`, `SuperEditor`)
- **`UdeMUser`** (Contient: `CanViewUdeMData`, `CanViewPartnerData`)
- **`PartnerUser`** (Contient: `CanViewPartnerData`)
- **`AuthenticatedUser`** (Ne contient aucune permission spéciale par défaut)

---

# Permissions : Rôle d'Héritage vs. Accès Contextuel

Ce document clarifie un concept fondamental de notre système de permissions : la coexistence et la nécessité de deux types d'héritage.

1.  **L'Héritage Contextuel** (géré par `AccessTreeServiceLive`)
2.  **L'Héritage des Rôles** (défini dans la structure des rôles)

Comprendre leur distinction est essentiel pour utiliser et maintenir correctement notre système de contrôle d'accès.

## Les Deux Piliers de Notre Système

Pensez à l'accès comme répondant à deux questions successives :
-   **"OÙ ?":** L'utilisateur a-t-il le droit d'être ici ? (Accès Contextuel)
-   **"QUOI ?":** Une fois sur place, qu'a-t-il le droit de faire ? (Héritage des Rôles)

### 1. L'Héritage Contextuel : Le "OÙ"

C'est la capacité du système à déduire un droit d'accès basé sur la hiérarchie des ressources.

-   **Principe** : Si un rôle est assigné à un utilisateur dans le contexte d'une `Unité`, le système lui accorde automatiquement une **"ligne de vue"** sur toutes les ressources qui se trouvent en dessous dans l'arbre hiérarchique (`infrastructures`, `équipements`).
-   **Ce que ça répond** : "Est-ce que cet utilisateur a un chemin d'accès valide vers l'infrastructure X ?"
-   **Exemple** : Un `UnitManager` assigné à l'Unité A a bien un accès contextuel à toutes les infrastructures de cette unité. Nous n'avons pas besoin d'un héritage de rôle pour établir ce chemin d'accès.

### 2. L'Héritage des Rôles : Le "QUOI"

C'est la capacité d'un rôle à agréger les **permissions** d'autres rôles.

-   **Principe** : Cet héritage ne définit pas *à quelles ressources* vous avez accès, mais *quelles actions* vous avez le droit de faire sur ces ressources une fois l'accès confirmé. Il agrège des **capacités**.
-   **Ce que ça répond** : "Maintenant que je suis devant l'infrastructure X, ai-je la permission `infrastructure:update` ou `infrastructure:delete` ?"
-   **Exemple** : En faisant hériter `UnitManager` de `InfrastructureManager`, nous nous assurons qu'il possède non seulement ses propres permissions de gestion d'unité, mais aussi toutes les capacités de gestion d'infrastructure.

## Comment ça Marche en Pratique : Une Vérification en Deux Étapes

Notre `UserPermissionsServiceLive` utilise ces deux piliers dans un ordre logique :

1.  **Étape 1 : Vérification de l'accès (Le "OÙ")**
    Le système utilise l'arbre contextuel pour vérifier si l'utilisateur a une ligne de vue sur la ressource.
    ```typescript
    userHasAccessToResource('userId', 'infrastructure', 'resourceId')
    ```
2.  **Étape 2 : Vérification de la permission (Le "QUOI")**
    Si l'accès est confirmé, le système vérifie si l'ensemble des rôles de l'utilisateur (incluant les permissions héritées) contient l'action demandée.
    ```typescript
    userHasPermission({ userId: 'userId', domain: 'infrastructure', action: 'update' })
    ```
    Si l'une de ces étapes échoue, l'opération est refusée.

## Le Scénario Problématique : Que se passerait-il sans l'héritage de rôle ?

Imaginons un instant que nous nous basions uniquement sur l'accès contextuel et que le rôle `UnitManager` n'hérite **pas** de `InfrastructureManager`.

**Configuration du Scénario :**
-   Bob a le rôle `UnitManager` sur l'Unité A.
-   L'Infrastructure X se trouve dans l'Unité A.
-   Le rôle `UnitManager` possède les permissions pour gérer les utilisateurs, mais pas celles pour gérer les infrastructures (qui sont dans le rôle `InfrastructureManager`).

**Déroulement de l'Opération :**
1.  **Vérification de l'accès :** Bob veut modifier l'Infrastructure X. Le système vérifie :
    -   `userHasAccessToResource('Bob', 'infrastructure', 'Infra-X-ID')` -> **SUCCÈS !**
    -   *Explication : L'arbre contextuel confirme que Bob a une ligne de vue sur l'infrastructure car elle est dans son unité.*

2.  **Vérification de la permission :** Le système vérifie maintenant si Bob a le droit de faire l'action `update` :
    -   `userHasPermission({ userId: 'Bob', domain: 'infrastructure', action: 'update' })` -> **ÉCHEC !**
    -   *Explication : Bien que Bob soit "devant la porte" de l'infrastructure, son trousseau de clés (ses permissions de rôle) ne contient pas la clé `infrastructure:update`. Cette clé se trouve dans le rôle `InfrastructureManager` dont il n'hérite pas dans ce scénario.*

> ### L'Analogie : La Clé de la Porte vs. Le Droit de Peindre les Murs
>
> -   L'**Héritage Contextuel** vous donne la **clé de la porte** de l'infrastructure.
> -   L'**Héritage de Rôle** vous donne le **droit de repeindre les murs** une fois que vous êtes à l'intérieur.
>
> Il faut les deux pour pouvoir effectuer une action.

## Conclusion

Le design où `UnitManager` hérite de `InfrastructureManager` n'est pas une redondance, mais une **nécessité fondamentale**. Il garantit qu'un gestionnaire à un niveau hiérarchique supérieur (`Unité`) possède automatiquement toutes les **capacités** des niveaux qu'il supervise (`Infrastructure`, `Équipement`).

Notre système est robuste parce qu'il sépare et combine intelligemment ces deux logiques :
-   Le **contexte d'assignation** détermine le **périmètre des ressources** accessibles.
-   L'**héritage de rôle** détermine l'**étendue des actions** possibles sur ces ressources.

---

## 3. Diagramme d'Héritage des Rôles

Ce diagramme visualise la hiérarchie complète. Une flèche de A vers B (`A --> B`) signifie que **A hérite de B**.

```mermaid
graph TD
    subgraph "Rôles Globaux (Sans Contexte)"
        SystemAdmin(SystemAdmin)
        SuperEditor(SuperEditor)
        SuperExplorer(SuperExplorer)
        UdeMUser(UdeM User)
        PartnerUser(Partner User)
        AuthenticatedUser(Authenticated User)
    end

    subgraph "Rôles Contextuels (Assignés avec Institution, Unité, etc.)"
        %% Management Roles
        InstitutionManager(InstitutionManager)
        UnitManager(UnitManager)
        InfrastructureManager(InfrastructureManager)
        EquipmentManager(EquipmentManager)
        
        %% Editor Roles
        InstitutionEditor(InstitutionEditor)
        UnitEditor(UnitEditor)
        InfrastructureEditor(InfrastructureEditor)

        %% Explorer Roles
        InstitutionExplorer(InstitutionExplorer)
        UnitExplorer(UnitExplorer)
        InfrastructureExplorer(InfrastructureExplorer)
    end

    subgraph "Rôles de Base (Templates Techniques)"
        BaseEditor(<b>BaseEditor</b>)
        BaseExplorer(<b>BaseExplorer</b>)
    end
    
    %% ---- Defining Connections ----

    %% Global Roles Inheritance
    SystemAdmin --> InstitutionManager
    SystemAdmin --> SuperEditor
    SuperEditor --> BaseEditor
    SuperEditor --> SuperExplorer
    SuperExplorer --> BaseExplorer
    
    %% Manager Roles Inheritance
    InstitutionManager --> UnitManager
    InstitutionManager --> InstitutionEditor
    UnitManager --> InfrastructureManager
    UnitManager --> UnitEditor
    InfrastructureManager --> EquipmentManager
    InfrastructureManager --> InfrastructureEditor
    EquipmentManager --> InfrastructureEditor
    
    %% Editor Roles Inheritance
    InstitutionEditor --> BaseEditor
    UnitEditor --> BaseEditor
    InfrastructureEditor --> BaseEditor
    
    %% Explorer Roles Inheritance
    InstitutionExplorer --> BaseExplorer
    UnitExplorer --> BaseExplorer
    InfrastructureExplorer --> BaseExplorer
    
    %% Base Roles Inheritance
    BaseEditor --> BaseExplorer

    %% ---- Styling ----
    style SystemAdmin fill:#c9f,stroke:#333,stroke-width:4px
    style BaseEditor fill:#ececff,stroke:#333,stroke-dasharray: 5 5, color:#000
    style BaseExplorer fill:#ececff,stroke:#333,stroke-dasharray: 5 5, color:#000

    style InstitutionManager fill:#d6b3ff, color:#000
    style UnitManager fill:#d6b3ff, color:#000
    style InfrastructureManager fill:#d6b3ff, color:#000
    style EquipmentManager fill:#d6b3ff, color:#000
    
    style InstitutionEditor fill:#a3c4ff, color:#000
    style UnitEditor fill:#a3c4ff, color:#000
    style InfrastructureEditor fill:#a3c4ff, color:#000

    style InstitutionExplorer fill:#ffeca3, color:#000
    style UnitExplorer fill:#ffeca3, color:#000
    style InfrastructureExplorer fill:#ffeca3, color:#000
```