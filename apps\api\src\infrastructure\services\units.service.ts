import { UnitsRepositoryLive } from '@/infrastructure/repositories/units.repository';
import type { CreateUnitPayload, UpdateUnitPayload } from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';

export class UnitsServiceLive extends Effect.Service<UnitsServiceLive>()(
  'UnitsServiceLive',
  {
    dependencies: [UnitsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const unitsRepository = yield* UnitsRepositoryLive;

      const getAllUnits = () => {
        return Effect.gen(function* () {
          return yield* unitsRepository.findAllUnits();
        });
      };

      const getUnitById = (id: string) => {
        return Effect.gen(function* () {
          const unit = yield* unitsRepository.findUnitById(id);
          if (!unit) {
            return yield* Effect.fail(
              new Error(`Unit with id ${id} not found`),
            );
          }
          return unit;
        });
      };

      const createUnit = (data: CreateUnitPayload) => {
        return Effect.gen(function* () {
          return yield* unitsRepository.createUnitWithTranslations({
            unit: {
              guidId: data.guidId,
              typeId: data.typeId,
              parentId: data.parentId,
              modifiedBy: data.modifiedBy,
            },
            translations: data.translations ? [...data.translations] : [],
          });
        });
      };

      const updateUnit = (params: UpdateUnitPayload) => {
        const { id, ...updateData } = params;
        return Effect.gen(function* () {
          const existingUnit = yield* unitsRepository.findUnitById(id);
          if (!existingUnit) {
            return yield* Effect.fail(
              new Error(`Unit with id ${id} not found`),
            );
          }

          return yield* unitsRepository.updateUnitWithTranslations({
            id: id,
            unit: {
              guidId: updateData.guidId,
              typeId: updateData.typeId,
              parentId: updateData.parentId,
              modifiedBy: updateData.modifiedBy,
            },
            translations: updateData.translations
              ? [...updateData.translations]
              : [],
          });
        });
      };

      const deleteUnit = (id: string) => {
        return Effect.gen(function* () {
          const existingUnit = yield* unitsRepository.findUnitById(id);
          if (!existingUnit) {
            return yield* Effect.fail(
              new Error(`Unit with id ${id} not found`),
            );
          }
          return yield* unitsRepository.deleteUnit(id);
        });
      };

      return {
        getAllUnits,
        getUnitById,
        createUnit,
        updateUnit,
        deleteUnit,
      } as const;
    }),
  },
) {}
