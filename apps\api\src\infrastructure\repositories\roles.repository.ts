import { ConfigLive } from '@/infrastructure/config/config.live';
import type {
  DbRoleInheritanceInput,
  DbRoleInput,
  DbRolePermissionGroupInput,
  DbRolePermissionInput,
} from '@rie/db-schema/entity-types';
import * as AuthDbSchema from '@rie/db-schema/schemas';
import { Database } from '@rie/postgres-db';
import { Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class RolesRepositoryLive extends Effect.Service<RolesRepositoryLive>()(
  'RolesRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;

      /**
       * Find all roles
       */
      const findAllRoles = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.roles.findMany({
            columns: {
              id: true,
              name: true,
              createdAt: true,
              updatedAt: true,
            },
          }),
        );
      });

      /**
       * Find a role by ID
       */
      const findRoleById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.roles.findFirst({
            where: eq(AuthDbSchema.roles.id, id),
            columns: {
              id: true,
              name: true,
              createdAt: true,
              updatedAt: true,
            },
            with: {
              rolePermissions: {
                columns: {
                  permissionId: true,
                  roleId: true,
                },
                with: {
                  permission: {
                    columns: {
                      id: true,
                      domain: true,
                      action: true,
                    },
                  },
                },
              },
              parentRoles: {
                columns: {
                  parentRoleId: true,
                  childRoleId: true,
                },
                with: {
                  parentRole: {
                    columns: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
              childRoles: {
                columns: {
                  parentRoleId: true,
                  childRoleId: true,
                },
                with: {
                  childRole: {
                    columns: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
            },
          }),
        );
      });

      /**
       * Check if a role with the given name already exists
       */
      const checkRoleExists = dbClient.makeQuery((execute, name: string) => {
        return execute((client) =>
          client.query.roles.findFirst({
            where: eq(AuthDbSchema.roles.name, name),
            columns: {
              id: true,
            },
          }),
        );
      });

      /**
       * Create a new role
       * This uses a transaction to ensure all related data is created atomically
       */
      interface CreateRoleParams {
        roleData: DbRoleInput;
        directPermissionIds: readonly string[];
        parentRoleIds: readonly string[];
        permissionGroupIds: readonly string[];
      }

      const createRole = ({
        roleData,
        directPermissionIds,
        parentRoleIds,
        permissionGroupIds,
      }: CreateRoleParams) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // 1. Create the role
            const [role] = yield* tx((client) =>
              client.insert(AuthDbSchema.roles).values(roleData).returning({
                id: AuthDbSchema.roles.id,
                name: AuthDbSchema.roles.name,
                createdAt: AuthDbSchema.roles.createdAt,
                updatedAt: AuthDbSchema.roles.updatedAt,
              }),
            );

            // 2. Add direct permissions if provided
            if (directPermissionIds.length > 0) {
              const rolePermissions: DbRolePermissionInput[] =
                directPermissionIds.map((permissionId) => ({
                  roleId: role.id,
                  permissionId,
                }));

              yield* tx((client) =>
                client
                  .insert(AuthDbSchema.rolePermissions)
                  .values(rolePermissions),
              );
            }

            // 3. Add role inheritance if provided
            if (parentRoleIds.length > 0) {
              const roleInheritance: DbRoleInheritanceInput[] =
                parentRoleIds.map((parentRoleId) => ({
                  childRoleId: role.id,
                  parentRoleId,
                }));

              yield* tx((client) =>
                client
                  .insert(AuthDbSchema.roleInheritance)
                  .values(roleInheritance),
              );
            }

            // 4. Add permission groups if provided
            if (permissionGroupIds.length > 0) {
              const rolePermissionGroups: DbRolePermissionGroupInput[] =
                permissionGroupIds.map((groupId) => ({
                  roleId: role.id,
                  groupId,
                }));

              yield* tx((client) =>
                client
                  .insert(AuthDbSchema.rolePermissionGroups)
                  .values(rolePermissionGroups),
              );
            }

            return role;
          });
        });
      };

      /**
       * Update a role
       */

      interface UpdateRoleParams extends CreateRoleParams {
        id: string;
      }
      const updateRole = ({
        id,
        roleData,
        directPermissionIds,
        parentRoleIds,
        permissionGroupIds,
      }: UpdateRoleParams) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // 1. Update the role
            const [role] = yield* tx((client) =>
              client
                .update(AuthDbSchema.roles)
                .set(roleData)
                .where(eq(AuthDbSchema.roles.id, id))
                .returning({
                  id: AuthDbSchema.roles.id,
                  name: AuthDbSchema.roles.name,
                  createdAt: AuthDbSchema.roles.createdAt,
                  updatedAt: AuthDbSchema.roles.updatedAt,
                }),
            );

            // 2. If direct permissions are provided, remove all existing direct permissions related to the role and add the new ones
            if (directPermissionIds.length > 0) {
              // Delete existing permissions
              yield* tx((client) =>
                client
                  .delete(AuthDbSchema.rolePermissions)
                  .where(eq(AuthDbSchema.rolePermissions.roleId, id)),
              );

              // Create new role permissions
              const rolePermissions: DbRolePermissionInput[] =
                directPermissionIds.map((permissionId) => ({
                  roleId: id,
                  permissionId,
                }));

              yield* tx((client) =>
                client
                  .insert(AuthDbSchema.rolePermissions)
                  .values(rolePermissions),
              );
            }

            // 3. Handle parent roles if provided
            if (parentRoleIds.length > 0) {
              yield* tx((client) =>
                client
                  .delete(AuthDbSchema.roleInheritance)
                  .where(eq(AuthDbSchema.roleInheritance.childRoleId, id)),
              );

              const roleInheritance: DbRoleInheritanceInput[] =
                parentRoleIds.map((parentRoleId) => ({
                  childRoleId: id,
                  parentRoleId,
                }));

              yield* tx((client) =>
                client
                  .insert(AuthDbSchema.roleInheritance)
                  .values(roleInheritance),
              );
            }

            // 4. Handle permission groups if provided
            if (permissionGroupIds.length > 0) {
              yield* tx((client) =>
                client
                  .delete(AuthDbSchema.rolePermissionGroups)
                  .where(eq(AuthDbSchema.rolePermissionGroups.roleId, id)),
              );

              const rolePermissionGroups: DbRolePermissionGroupInput[] =
                permissionGroupIds.map((groupId) => ({
                  roleId: id,
                  groupId,
                }));

              yield* tx((client) =>
                client
                  .insert(AuthDbSchema.rolePermissionGroups)
                  .values(rolePermissionGroups),
              );
            }

            return role;
          });
        });
      };

      /**
       * Delete a role
       * This will cascade delete all related data due to foreign key constraints
       */
      const deleteRole = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(AuthDbSchema.roles)
            .where(eq(AuthDbSchema.roles.id, id))
            .returning({ id: AuthDbSchema.roles.id }),
        );
      });

      /**
       * Get all permissions for a role (direct, inherited, and from groups)
       */
      const getRolePermissions = dbClient.makeQuery(
        (execute, roleId: string) => {
          return execute((client) =>
            client.query.roles.findFirst({
              where: eq(AuthDbSchema.roles.id, roleId),
              columns: {
                id: true,
                name: true,
              },
              with: {
                // Direct permissions
                rolePermissions: {
                  columns: {},
                  with: {
                    permission: {
                      columns: {
                        id: true,
                        domain: true,
                        action: true,
                      },
                    },
                  },
                },
                // Inherited permissions through parent roles
                parentRoles: {
                  columns: {},
                  with: {
                    parentRole: {
                      columns: {
                        id: true,
                        name: true,
                      },
                      with: {
                        rolePermissions: {
                          columns: {},
                          with: {
                            permission: {
                              columns: {
                                id: true,
                                domain: true,
                                action: true,
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            }),
          );
        },
      );

      /**
       * Get all permission groups for a role
       */
      const getRolePermissionGroups = dbClient.makeQuery(
        (execute, roleId: string) => {
          return execute((client) =>
            client.query.rolePermissionGroups.findMany({
              where: eq(AuthDbSchema.rolePermissionGroups.roleId, roleId),
              columns: {
                roleId: true,
                groupId: true,
              },
              with: {
                group: {
                  columns: {
                    id: true,
                    name: true,
                    description: true,
                  },
                },
              },
            }),
          );
        },
      );

      return {
        findAllRoles,
        findRoleById,
        checkRoleExists,
        createRole,
        updateRole,
        deleteRole,
        getRolePermissions,
        getRolePermissionGroups,
      } as const;
    }),
  },
) {}
