
services:

  rie-frontend-nextjs.local:
    container_name: rie-frontend-nextjs.local
    hostname: rie-frontend-nextjs.local
    image: local/workspace:rie-frontend-nextjs
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - USERNAME=$USER
        - USER_UID=$UID
        ### env.ts
        - PORT=${PORT}
        - NEXT_PUBLIC_BASE_PATH=${NEXT_PUBLIC_BASE_PATH}
        - NEXT_PUBLIC_ORIGIN_URL=${NEXT_PUBLIC_ORIGIN_URL}
        - NEXT_PUBLIC_RIE_API_URL=${NEXT_PUBLIC_RIE_API_URL}
        - NEXT_PUBLIC_RIE_AUTH_URL=${NEXT_PUBLIC_RIE_AUTH_URL}
        - RIE_AUTH_CLIENT_ID=${RIE_AUTH_CLIENT_ID}
        - RIE_AUTH_CLIENT_SECRET=${RIE_AUTH_CLIENT_SECRET}
        - NEXT_PUBLIC_POSTHOG_HOST=${NEXT_PUBLIC_POSTHOG_HOST}
        - NEXT_PUBLIC_POSTHOG_KEY=${NEXT_PUBLIC_POSTHOG_KEY}
    command: sleep infinity
    ports:
      - "${PORT:-3000}:${PORT:-3000}"
    volumes:
      - ../.:/workspace/projects/rie/rie-frontend-nextjs
      - ~/.ssh/:/home/<USER>/.ssh
