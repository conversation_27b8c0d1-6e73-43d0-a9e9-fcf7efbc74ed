import {
  BuildingNotFoundError,
  CampusNotFoundError,
  FundingProjectNotFoundError,
  PermissionAlreadyExistsError,
  PermissionGroupAlreadyExistsError,
  PermissionGroupNotFoundError,
  PermissionNotFoundError,
  RoleAlreadyExistsError,
  RoleNotFoundError,
  UserNotFoundError,
} from '@rie/domain/errors';
import { Database } from '@rie/postgres-db';
import * as Cause from 'effect/Cause';
import * as Exit from 'effect/Exit';
import type { Context } from 'hono';

interface DatabaseErrorLike {
  code?: string;
  constraint?: string;
  detail?: string;
  message?: string;
}

export const handleEffectError = <T>(
  ctx: Context,
  exit: Exit.Exit<T, unknown>,
): Response | null => {
  if (Exit.isSuccess(exit)) {
    return null;
  }

  const squashed = Cause.squash(exit.cause);

  // Handle domain-specific errors
  if (squashed instanceof BuildingNotFoundError) {
    return ctx.json(
      { error: `Building with id ${squashed.id} not found` },
      404,
    );
  }

  if (squashed instanceof CampusNotFoundError) {
    return ctx.json({ error: `Campus with id ${squashed.id} not found` }, 404);
  }

  if (squashed instanceof FundingProjectNotFoundError) {
    return ctx.json(
      { error: `Funding project with id ${squashed.id} not found` },
      404,
    );
  }

  if (squashed instanceof PermissionNotFoundError) {
    return ctx.json(
      { error: `Permission with id ${squashed.id} not found` },
      404,
    );
  }

  if (squashed instanceof PermissionAlreadyExistsError) {
    return ctx.json(
      {
        error: `Permission ${squashed.domain}:${squashed.action} already exists`,
      },
      409,
    );
  }

  if (squashed instanceof PermissionGroupNotFoundError) {
    return ctx.json(
      { error: `Permission group with id ${squashed.id} not found` },
      404,
    );
  }

  if (squashed instanceof PermissionGroupAlreadyExistsError) {
    return ctx.json(
      { error: `Permission group ${squashed.name} already exists` },
      409,
    );
  }

  if (squashed instanceof RoleNotFoundError) {
    return ctx.json({ error: `Role with id ${squashed.id} not found` }, 404);
  }

  if (squashed instanceof RoleAlreadyExistsError) {
    return ctx.json({ error: `Role ${squashed.name} already exists` }, 409);
  }

  if (squashed instanceof UserNotFoundError) {
    return ctx.json({ error: `User with id ${squashed.id} not found` }, 404);
  }

  // Handle database errors
  if (squashed instanceof Database.DatabaseError) {
    // Handle foreign key violations specifically
    if (squashed.cause?.code === '23503') {
      const constraint = squashed.cause.constraint;
      const detail = squashed.cause.detail;

      // Parse the constraint name to provide more helpful error messages
      if (constraint?.includes('holder_id')) {
        return ctx.json(
          {
            error: 'Invalid holderId: Person not found',
            details: detail,
            constraint: constraint,
            hint: 'Use GET /api/v2/people to get a list of valid person IDs',
          },
          422,
        );
      }

      if (constraint?.includes('type_id')) {
        return ctx.json(
          {
            error: 'Invalid typeId: Funding project type not found',
            details: detail,
            constraint: constraint,
            hint: 'Use GET /api/v2/funding-project-types to get a list of valid type IDs',
          },
          422,
        );
      }

      // Generic foreign key violation
      return ctx.json(
        {
          error: 'Foreign key constraint violation',
          details: detail,
          constraint: constraint,
        },
        422,
      );
    }

    // Handle other database errors
    return ctx.json(
      {
        error: 'Database error',
        details: squashed.cause?.message || squashed.message,
        code: squashed.cause?.code,
      },
      400,
    );
  }

  // Check if it's a raw database error that wasn't wrapped
  if (squashed && typeof squashed === 'object' && 'code' in squashed) {
    const dbError = squashed as DatabaseErrorLike;

    if (dbError.code === '23503') {
      const constraint = dbError.constraint;
      const detail = dbError.detail;

      // Parse the constraint name to provide more helpful error messages
      if (constraint?.includes('holder_id')) {
        return ctx.json(
          {
            error: 'Invalid holderId: Person not found',
            details: detail,
            constraint: constraint,
            hint: 'Use GET /api/v2/people to get a list of valid person IDs',
          },
          422,
        );
      }

      if (constraint?.includes('type_id')) {
        return ctx.json(
          {
            error: 'Invalid typeId: Funding project type not found',
            details: detail,
            constraint: constraint,
            hint: 'Use GET /api/v2/funding-project-types to get a list of valid type IDs',
          },
          422,
        );
      }

      // Generic foreign key violation
      return ctx.json(
        {
          error: 'Foreign key constraint violation',
          details: detail,
          constraint: constraint,
        },
        422,
      );
    }

    // Handle other raw database errors
    return ctx.json(
      {
        error: 'Database error',
        details: dbError.message,
        code: dbError.code,
      },
      400,
    );
  }

  // Handle generic errors
  return ctx.json({ error: 'An unexpected error occurred' }, 500);
};
