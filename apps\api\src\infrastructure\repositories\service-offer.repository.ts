import { serviceOffers } from '@rie/db-schema/schemas';
import { Database } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';

export class ServiceOfferRepositoryLive extends Effect.Service<ServiceOfferRepositoryLive>()(
  'ServiceOfferRepositoryLive',
  {
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;

      const findById = dbClient.makeQuery((execute, id: string) =>
        execute((client) =>
          client.query.serviceOffers.findFirst({
            where: eq(serviceOffers.id, id),
            columns: {
              id: true,
              isForClinicalResearch: true,
              highlightService: true,
              createdBy: true,
              createdAt: true,
              updatedAt: true,
            },
            with: {
              translations: {
                columns: {
                  locale: true,
                  name: true,
                  description: true,
                  serviceConditions: true,
                },
              },
            },
          }),
        ),
      );

      return { findById } as const;
    }),
  },
) {}
