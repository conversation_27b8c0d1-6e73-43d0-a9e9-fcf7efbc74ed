import { ConfigLive } from '@/infrastructure/config/config.live';
import { PermissionsRepositoryLive } from '@/infrastructure/repositories/permissions.repository';
import { PermissionsServiceLive } from '@/infrastructure/services/permissions.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const PermissionServicesLayer = Layer.mergeAll(
  PermissionsRepositoryLive.Default,
  PermissionsServiceLive.Default,
);

export const PermissionsRuntime = ManagedRuntime.make(
  Layer.provide(
    PermissionServicesLayer,
    Layer.merge(ConfigLive.Default, PgDatabaseLive),
  ),
);
