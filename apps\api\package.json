{"name": "@rie/api", "version": "0.1.0", "private": true, "scripts": {"dev": "bun run --hot src/index.ts", "type-check": "tsc --noEmit", "db:generate-pg": "drizzle-kit generate --config src/infrastructure/database/postgres/drizzle.config.ts", "db:generate-mysql": "drizzle-kit generate --config src/infrastructure/database/mysql/drizzle.config.ts", "db:migrate-pg": "drizzle-kit migrate --config src/infrastructure/database/postgres/drizzle.config.ts", "db:migrate-mysql": "drizzle-kit migrate --config src/infrastructure/database/mysql/drizzle.config.ts", "db:seed-pg": "bun run src/infrastructure/database/seed.ts", "db:seed-mysql": "bun run src/infrastructure/database/seed.ts", "db:studio-pg": "drizzle-kit studio --config src/infrastructure/database/postgres/drizzle.config.ts", "db:studio-mysql": "drizzle-kit studio --config src/infrastructure/database/mysql/drizzle.config.ts", "lint": "pnpm biome check --write"}, "dependencies": {"@effect/platform": "^0.82.0", "@hono/auth-js": "^1.0.15", "@hono/effect-validator": "^1.2.0", "@hono/swagger-ui": "^0.5.1", "@rie/constants": "workspace:*", "@paralleldrive/cuid2": "^2.2.2", "@rie/auth": "workspace:*", "@rie/db-schema": "workspace:*", "@rie/domain": "workspace:*", "@rie/postgres-db": "workspace:*", "@types/pg": "^8.15.1", "better-auth": "^1.2.7", "drizzle": "^1.4.0", "drizzle-orm": "^0.43.1", "drizzle-seed": "^0.3.1", "effect": "^3.15.0", "hono": "^4.7.9", "hono-openapi": "^0.4.8", "mime-types": "^3.0.1", "mysql2": "^3.14.1", "pg": "^8.16.0", "postgres": "^3.4.5"}, "devDependencies": {"@rie/biome-config": "workspace:*", "@types/bun": "^1.2.13", "drizzle-kit": "^0.31.1", "typescript": "^5.8.3"}}