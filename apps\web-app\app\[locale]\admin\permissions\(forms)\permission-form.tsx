'use client';

import {
  type PermissionFormSchema,
  permissionFormSchema,
} from '@/app/[locale]/admin/permissions/(forms)/permission-form.schema';
import { FieldInfo } from '@/components/FieldInfo';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import type { RequestStatus } from '@/types/common';
import { effectTsResolver } from '@hookform/resolvers/effect-ts';
import { useForm } from 'react-hook-form';
// This can be used to edit an existing permission
interface PermissionFormProps {
  initialData?: PermissionFormSchema;
  onSubmit: (data: PermissionFormSchema) => void;
  status: RequestStatus;
}

export function PermissionForm({
  initialData,
  onSubmit,
  status,
}: PermissionFormProps) {
  const defaultValues: Partial<PermissionFormSchema> = {
    domain: initialData?.domain ?? '',
    action: initialData?.action ?? '',
    description: initialData?.description ?? '',
  };

  const form = useForm<PermissionFormSchema>({
    resolver: effectTsResolver(permissionFormSchema),
    reValidateMode: 'onChange',
    defaultValues,
  });

  const { formState } = form;
  const { isDirty, isValid } = formState;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">
          {initialData ? 'Edit Permission' : 'Create Permission'}
        </CardTitle>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent className="space-y-4">
            {formState.errors.root && (
              <div className="bg-red-50 p-3 rounded-md text-red-500 text-sm mb-4">
                {formState.errors.root.message}
              </div>
            )}
            <FormField
              control={form.control}
              name="domain"
              render={({ field }) => (
                <FormItem>
                  <LabelTooltip label="Domain" />
                  <FormControl>
                    <Input
                      placeholder="e.g., equipment, infrastructure"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    The area of functionality this permission applies to
                  </FormDescription>
                  <FieldInfo>
                    <FormMessage />
                  </FieldInfo>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="action"
              render={({ field }) => (
                <FormItem>
                  <LabelTooltip label="Action" />
                  <FormControl>
                    <Input
                      placeholder="e.g., read, update, delete"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    The specific action this permission allows
                  </FormDescription>
                  <FieldInfo>
                    <FormMessage />
                  </FieldInfo>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <LabelTooltip label="Description" />
                  <FormControl>
                    <Textarea
                      placeholder="Optional explanation of what this permission allows"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide additional context about this permission (optional)
                  </FormDescription>
                  <FieldInfo>
                    <FormMessage />
                  </FieldInfo>
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter className="flex justify-end gap-2">
            <Button
              type="submit"
              disabled={
                status === 'pending' || (!isDirty && !initialData) || !isValid
              }
            >
              {status === 'pending'
                ? 'Saving...'
                : initialData
                  ? 'Update Permission'
                  : 'Create Permission'}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
