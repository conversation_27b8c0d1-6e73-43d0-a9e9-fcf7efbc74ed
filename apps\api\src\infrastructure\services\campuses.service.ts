import { CampusesRepositoryLive } from '@/infrastructure/repositories/campuses.repository';
import { CampusNotFoundError } from '@rie/domain/errors';
import type {
  CreateCampusPayload,
  UpdateCampusPayload,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';

export class CampusesServiceLive extends Effect.Service<CampusesServiceLive>()(
  'CampusesServiceLive',
  {
    dependencies: [CampusesRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const campusesRepository = yield* CampusesRepositoryLive;

      const getAllCampuses = () => {
        return Effect.gen(function* () {
          return yield* campusesRepository.findAllCampuses();
        });
      };

      const getCampusById = (id: string) => {
        return Effect.gen(function* () {
          const campus = yield* campusesRepository.findCampusById(id);
          if (!campus) {
            return yield* Effect.fail(new CampusNotFoundError({ id }));
          }
          return campus;
        });
      };

      const createCampus = (data: CreateCampusPayload) => {
        return Effect.gen(function* () {
          return yield* campusesRepository.createCampus({
            campus: {
              sadId: data.sadId,
              institutionId: data.institutionId,
            },
            translations: data.translations ? [...data.translations] : [],
            modifiedBy: data.modifiedBy ?? '',
          });
        });
      };

      const updateCampus = (params: UpdateCampusPayload) => {
        const { id, ...updateData } = params;
        return Effect.gen(function* () {
          const existingCampus = yield* campusesRepository.findCampusById(id);
          if (!existingCampus) {
            return yield* Effect.fail(new CampusNotFoundError({ id }));
          }

          const updated =
            yield* campusesRepository.updateCampus({
              id: id,
              campus: {
                sadId: updateData.sadId,
                institutionId: updateData.institutionId,
              },
              modifiedBy: updateData.modifiedBy ?? '',
              translations: updateData.translations
                ? [...updateData.translations]
                : [],
            });

          if (!updated) {
            return yield* Effect.fail(new CampusNotFoundError({ id }));
          }

          return updated;
        });
      };

      const deleteCampus = (id: string) => {
        return Effect.gen(function* () {
          const existingCampus = yield* campusesRepository.findCampusById(id);
          if (!existingCampus) {
            return yield* Effect.fail(new CampusNotFoundError({ id }));
          }
          const result = yield* campusesRepository.deleteCampus(id);
          return result.length > 0;
        });
      };

      return {
        getAllCampuses,
        getCampusById,
        createCampus,
        updateCampus,
        deleteCampus,
      } as const;
    }),
  },
) { }
