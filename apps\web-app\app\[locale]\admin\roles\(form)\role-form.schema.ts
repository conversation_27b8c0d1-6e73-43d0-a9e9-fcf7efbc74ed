import {
  createOptionalStringOfMaxLengthSchema,
  createRequiredStringOfMaxLengthSchema,
} from '@rie/domain/schemas';
import * as Schema from 'effect/Schema';

export const roleFormSchema = Schema.Struct({
  name: createRequiredStringOfMaxLengthSchema({
    fieldMaxLength: 10,
    errorMessages: {
      required: () => 'Domain is required',
      maxLength: (issue) =>
        `Domain must be ${issue._tag.length} characters or less`,
    },
  }),
  description: createOptionalStringOfMaxLengthSchema({
    fieldMaxLength: 1500,
    maxLengthErrorMessage: (issue) =>
      `Description must be ${issue._tag.length} characters or less`,
  }),
  directPermissionIds: Schema.Array(Schema.String),
  permissionGroupIds: Schema.Array(Schema.String).pipe(
    Schema.minItems(1, {
      message: () => 'At least one permission group is required',
    }),
  ),
  parentRoleIds: Schema.Array(Schema.String),
});

export type RoleFormSchema = Schema.Schema.Type<typeof roleFormSchema>;
