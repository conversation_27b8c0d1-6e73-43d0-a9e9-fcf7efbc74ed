import { auth } from '@rie/auth';
import type { Context, Next } from 'hono';

/**
 * Middleware to set user and session context for each request.
 * @param ctx - The Hono context object.
 * @param next - The next middleware function.
 */
export const userContextMiddleware = async (ctx: Context, next: Next) => {
  const session = await auth.api.getSession({ headers: ctx.req.raw.headers });

  if (!session) {
    ctx.set('user', null);
    ctx.set('session', null);
    return next();
  }

  ctx.set('user', session.user);
  ctx.set('session', session.session);
  return next();
};

// Make the middleware Hono-compatible
export const createUserContextMiddleware = () => userContextMiddleware;
