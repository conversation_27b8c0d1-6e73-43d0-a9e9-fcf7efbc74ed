import { ConfigLive } from '@/infrastructure/config/config.live';
import { RolesRepositoryLive } from '@/infrastructure/repositories/roles.repository';
import { RolesServiceLive } from '@/infrastructure/services/roles.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const RoleServicesLayer = Layer.mergeAll(
  RolesRepositoryLive.Default,
  RolesServiceLive.Default,
);

export const RolesRuntime = ManagedRuntime.make(
  Layer.provide(
    RoleServicesLayer,
    Layer.merge(ConfigLive.Default, PgDatabaseLive),
  ),
);
