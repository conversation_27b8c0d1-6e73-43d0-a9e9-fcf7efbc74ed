'use client';

import { PermissionForm } from '@/app/[locale]/admin/permissions/(forms)/permission-form';
import type { PermissionFormSchema } from '@/app/[locale]/admin/permissions/(forms)/permission-form.schema';
import { useCreatePermission } from '@/hooks/admin/permissions/permissions.hook';

export const NewPermission = () => {
  const { mutate, status } = useCreatePermission();
  const handleOnSubmit = (data: PermissionFormSchema) => {
    mutate(data);
  };

  return <PermissionForm onSubmit={handleOnSubmit} status={status} />;
};
