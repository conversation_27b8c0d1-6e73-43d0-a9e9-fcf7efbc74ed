import buildingMessages from '@/locales/en/buildings.locale.json';
import commonMessages from '@/locales/en/common.locale.json';
import serviceContractMessages from '@/locales/en/contract-service.locale.json';
import directoryMessages from '@/locales/en/directory.locale.json';
import manufacturerMessages from '@/locales/en/directory.locale.json';
import establishmentsMessages from '@/locales/en/directory.locale.json';
import equipmentMessages from '@/locales/en/equipments.locale.json';
import errorMessages from '@/locales/en/error.locale.json';
import facetMessages from '@/locales/en/facets.locale.json';
import financingProjectMessages from '@/locales/en/financing-project.locale.json';
import formMessages from '@/locales/en/forms.locale.json';
import infrastructureMessages from '@/locales/en/infrastructures.locale.json';
import navigationMessages from '@/locales/en/navigation.locale.json';
import { AppStoreProvider } from '@/providers/app-store-provider';
import QueryProvider from '@/providers/query-provider';
import type { Preview } from '@storybook/react';
import '../app/globals.css';
import { NextIntlClientProvider } from 'next-intl';

const preview: Preview = {
  decorators: [
    (Story) => {
      return (
        <AppStoreProvider>
          <NextIntlClientProvider
            locale="en"
            messages={{
              building: buildingMessages,
              common: commonMessages,
              contractService: serviceContractMessages,
              directory: directoryMessages,
              equipment: equipmentMessages,
              error: errorMessages,
              establishments: establishmentsMessages,
              facet: facetMessages,
              financingProjects: financingProjectMessages,
              forms: formMessages,
              infrastructures: infrastructureMessages,
              manufacturers: manufacturerMessages,
              navigation: navigationMessages,
            }}
          >
            <QueryProvider>
              <Story />
            </QueryProvider>
          </NextIntlClientProvider>
        </AppStoreProvider>
      );
    },
  ],
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
};

export default preview;
