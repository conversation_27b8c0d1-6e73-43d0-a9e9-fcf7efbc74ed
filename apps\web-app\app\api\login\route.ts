import { REFERER_KEY } from '@/constants/common';
import { env } from '@/env';
import { getAuthorizationUrl } from '@/services/utils/auth';
import { cookies } from 'next/headers';

export const GET = async (req: Request) => {
  const referer = req.headers.get('referer');
  const pathname = referer ? new URL(referer).pathname : '/';

  // Extract domain and protocol from request URL
  const { protocol } = new URL(req.url);
  const originUrl = new URL(env.NEXT_PUBLIC_ORIGIN_URL);
  const cookieDomain = originUrl.hostname;
  const isSecure = protocol === 'https:';
  const basePath = env.NEXT_PUBLIC_BASE_PATH || '/';

  (await cookies()).set({
    name: REFERER_KEY,
    sameSite: 'lax',
    secure: isSecure,
    value: pathname,
    path: basePath,
    domain: cookieDomain,
  });

  const url = await getAuthorizationUrl();
  return Response.redirect(url);
};
