# Microservice Architecture

This document outlines the architecture of our microservice, explaining the key decisions and patterns used throughout the project.

## Technology Stack

- **Runtime**: Bun
- **API Framework**: Hono
- **Database**: PostgreSQL
- **Database**: MySQL (Used in the PHP legacy API)
- **ORM**: Drizzle
- **Effect Management**: Effect-ts
- **API Documentation**: OpenAPI (via hono-openapi)

## Core Principles

- **Domain-Driven Design (DDD)**: Business logic is centralized in the domain layer
- **Dependency Injection**: Using Effect-ts for managing dependencies
- **Clean Architecture**: Clear separation of concerns with independent layers
- **Type Safety**: Leveraging TypeScript and Effect-ts for compile-time guarantees
- **Functional Programming**: Using Effect-ts for pure, composable functions

## Project Structure

This project is part of a monorepo structure with shared packages. The API application structure is as follows:

```
src/
├── api/                        // API layer
│   └── v2/                     // API version 2
│       ├── middleware/         // API-specific middleware
│       └── routes/             // Route definitions
│
├── application/                // Application use cases
│   ├── ports/                  // Input/Output ports (interfaces)
│   │   └── repositories/       // Contains only interfaces, Defines what operations are possible, No implementation details
│   └── use-cases/              // Business use cases
│
├── infrastructure/             // External services implementations
│   ├── config/                 // Configuration management
│   ├── http/                   // HTTP-related functionality
│   ├── repositories/           // Contains concrete implementations of interfaces
│   ├── runtimes/               // Runtime configurations and setups
│   └── services/               // External service implementations
│
├── app.ts                      // Application setup
└── index.ts                    // Application entry point
```

### Related Packages

The project uses several shared packages from the monorepo:

```
packages/
├── domain/                     // Domain models and business logic
│   ├── src/
│       ├── schemas/            // Domain schemas
│       ├── transformers/       // Data transformers
│       ├── types/              // Type definitions
│       └── utils/              // Utility functions
│
├── mysql-db/                   // MySQL database functionality
│   └── src/
│       └── schema/             // Drizzle schema definitions
│
└── postgres-db/                // PostgreSQL database functionality
    └── src/
        └── types/              // Database type definitions
```

## Layer Explanations

### Domain Layer (`packages/domain`)

The domain layer is the heart of the application, containing the business logic and rules. It is implemented as a shared package in the monorepo.

**Key Decisions**:
- Using Effect's Data module for domain models
  - Provides runtime type safety
  - Enables powerful pattern matching
  - Ensures immutability
- Clear separation of domain errors
  - Each error type extends Data.TaggedError
  - Enables precise error handling
  - Maintains type safety throughout the application

### Application Layer (`src/application`)

The application layer orchestrates the flow of data and implements use cases.

**Key Decisions**:
- Ports and Adapters pattern
  - Contains only interfaces
  - Defines what operations are possible
  - No implementation details
  - Clear interfaces for external dependencies
  - Easy to swap implementations
  - Better testability
- Use cases as pure functions
  - Single responsibility
  - Clear input/output contracts
  - Composable through Effect

### Infrastructure Layer (`src/infrastructure`)

The infrastructure layer implements interfaces defined in the application layer.

**Key Decisions**:
- Database access via shared database packages
  - Type-safe queries with Drizzle ORM
  - Better performance than traditional ORMs
  - Native TypeScript support
- Implementation of ports
  - Concrete implementations of interfaces
  - External service integrations
  - Repository implementations

### API Layer (`src/api/v2`)

The API layer handles HTTP concerns and API documentation.

**Key Decisions**:
- Hono for routing
  - Lightweight and fast
  - Built for edge runtime
  - Good TypeScript support
- OpenAPI integration
  - Automatic documentation
  - Type-safe request/response validation
  - Clear API contracts

## Key Architectural Patterns

### Dependency Injection with Effect-ts

We use Effect-ts for dependency injection because:
- Type-safe dependency management
- Runtime dependency tracking
- Easy testing through dependency substitution
- Better error handling capabilities

Example:
```typescript
interface CreateUserDeps {
  serviceOfferRepository: ServiceOfferRepository
  emailService: EmailService
}

const createServiceOffer = Effect.gen(function* (_) {
  const { serviceOfferRepository, emailService } = yield* _(Effect.service<CreateServiceOfferDeps>())
  // Implementation
})
```

### Error Handling

Error handling is done through Effect's error channel:
- Type-safe error handling
- Composable error handling
- Clear error boundaries
- Pattern matching on errors

Example:
```typescript
export class ServiceOfferNotFoundError extends Data.TaggedError("ServiceOfferNotFoundError")<{
  readonly fieldErrorMessage: string
}> {}
```

### Domain Models

Domain models use Effect's Data module:
- Runtime type safety
- Pattern matching capabilities
- Immutable by default
- Clear domain boundaries

We use different Data constructors based on our needs:

1. `Data.Class` for simple value objects and entities:
```typescript
export class ServiceOffer extends Data.Class<{
  readonly id: string
  readonly email: Email
  readonly name: Name
  readonly status: ServiceOfferStatus
}> {}
```

2. `Data.TaggedClass` for types that need runtime discrimination:
```typescript
// Example of a payment method that could be different types
export class PaymentMethod extends Data.TaggedClass<{
  readonly _tag: "CreditCard" | "BankTransfer" | "PayPal"
  readonly id: string
  readonly amount: number
}> {}
```

3. `Data.TaggedError` specifically for error types:
```typescript
export class ServiceOfferNotFoundError extends Data.TaggedError("ServiceOfferNotFoundError")<{
  readonly fieldErrorMessage: string
}> {}
```

## Testing Strategy

The architecture supports different types of tests:

1. **Unit Tests**
   - Testing domain logic in isolation
   - Pure function testing
   - No external dependencies

2. **Integration Tests**
   - Testing use cases with real dependencies
   - Database integration testing
   - External service testing

3. **API Tests**
   - Testing HTTP endpoints
   - Request/response validation
   - Error handling testing

## Best Practices

1. **Domain Logic**
   - Keep domain models pure and immutable
   - Use smart constructors for validation
   - Express business rules in the domain layer

2. **Error Handling**
   - Use specific error types
   - Handle errors at boundaries
   - Provide meaningful error messages

3. **Dependency Management**
   - Explicit dependencies
   - Clear interface contracts
   - Easy to test implementations

4. **API Design**
   - RESTful principles
   - Clear documentation
   - Consistent error responses

## Getting Started

1. Clone the repository
2. Install dependencies: `bun install`
3. Set up environment variables
4. Run migrations: `bun run migrate`
5. Start the server: `bun run dev`
