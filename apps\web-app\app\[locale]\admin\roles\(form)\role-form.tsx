'use client';

import {
  type RoleFormSchema,
  roleFormSchema,
} from '@/app/[locale]/admin/roles/(form)/role-form.schema';
import { FieldInfo } from '@/components/FieldInfo';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { useGetAllPermissionsGroups } from '@/hooks/admin/permissions/permissions-groups.hook';
import { useGetAllPermissions } from '@/hooks/admin/permissions/permissions.hook';
import { useGetAllRoles } from '@/hooks/admin/permissions/roles.hook';
import type { RequestStatus } from '@/types/common';
import { MultiSelectNew } from '@/ui/multi-select-new';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/ui/tabs';
import { effectTsResolver } from '@hookform/resolvers/effect-ts';
import { useForm } from 'react-hook-form';

interface RoleFormProps {
  initialData?: RoleFormSchema;
  onSubmit: (data: RoleFormSchema) => void;
  status: RequestStatus;
}

export function RoleForm({ initialData, onSubmit, status }: RoleFormProps) {
  const defaultValues: Partial<RoleFormSchema> = {
    name: initialData?.name ?? '',
    description: initialData?.description ?? '',
    directPermissionIds: initialData?.directPermissionIds ?? [],
    permissionGroupIds: initialData?.permissionGroupIds ?? [],
    parentRoleIds: initialData?.parentRoleIds ?? [],
  };

  const form = useForm<RoleFormSchema>({
    resolver: effectTsResolver(roleFormSchema),
    reValidateMode: 'onChange',
    defaultValues,
  });

  const { formState } = form;
  const { isDirty, isValid } = formState;

  const { data: permissions, status: permissionsStatus } =
    useGetAllPermissions();
  const { data: permissionGroups, status: permissionGroupsStatus } =
    useGetAllPermissionsGroups();
  const { data: roles, status: rolesStatus } = useGetAllRoles();

  if (
    permissionsStatus === 'pending' ||
    permissionGroupsStatus === 'pending' ||
    rolesStatus === 'pending'
  ) {
    return <LoadingResource />;
  }

  if (
    permissionsStatus === 'error' ||
    permissionGroupsStatus === 'error' ||
    rolesStatus === 'error'
  ) {
    return <div>Error</div>;
  }

  console.log('status', status);

  if (
    permissionsStatus === 'success' &&
    permissionGroupsStatus === 'success' &&
    rolesStatus === 'success'
  ) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl">
            {initialData ? 'Edit Role' : 'Create Role'}
          </CardTitle>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className="space-y-4">
              {formState.errors.root && (
                <div className="bg-red-50 p-3 rounded-md text-red-500 text-sm mb-4">
                  {formState.errors.root.message}
                </div>
              )}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <LabelTooltip label="Name" />
                    <FormControl>
                      <Input
                        placeholder="e.g., Admin, Editor, Viewer"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      A descriptive name for this role
                    </FormDescription>
                    <FieldInfo>
                      <FormMessage />
                    </FieldInfo>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <LabelTooltip label="Description" />
                    <FormControl>
                      <Textarea
                        placeholder="Optional explanation of what this role allows"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide additional context about this role
                    </FormDescription>
                    <FieldInfo>
                      <FormMessage />
                    </FieldInfo>
                  </FormItem>
                )}
              />
              <div className="space-y-4">
                <h3 className="text-lg font-medium">
                  Permissions and Inheritance
                </h3>
                <Separator />
                <Tabs defaultValue="direct-permissions" className="w-full">
                  <TabsList className="grid grid-cols-3 mb-4">
                    <TabsTrigger value="direct-permissions">
                      Direct Permissions
                    </TabsTrigger>
                    <TabsTrigger value="permission-groups">
                      Permission Groups
                    </TabsTrigger>
                    <TabsTrigger value="parent-roles">Parent Roles</TabsTrigger>
                  </TabsList>
                  <TabsContent value="direct-permissions" className="space-y-4">
                    <FormField
                      control={form.control}
                      name="directPermissionIds"
                      render={({ field }) => (
                        <FormItem>
                          <LabelTooltip label="Direct Descriptions" />
                          <FormControl>
                            <MultiSelectNew
                              onValueChange={field.onChange}
                              options={permissions ?? []}
                              defaultValue={field.value}
                            />
                          </FormControl>
                          <FieldInfo>
                            <FormMessage />
                          </FieldInfo>
                        </FormItem>
                      )}
                    />
                  </TabsContent>
                  <TabsContent value="permission-groups" className="space-y-4">
                    <FormField
                      control={form.control}
                      name="permissionGroupIds"
                      render={({ field }) => (
                        <FormItem>
                          <LabelTooltip label="Permission Groups" />
                          <FormControl>
                            <MultiSelectNew
                              onValueChange={field.onChange}
                              options={permissionGroups ?? []}
                              defaultValue={field.value}
                            />
                          </FormControl>
                          <FieldInfo>
                            <FormMessage />
                          </FieldInfo>
                        </FormItem>
                      )}
                    />
                  </TabsContent>
                  <TabsContent value="parent-roles" className="space-y-4">
                    <FormField
                      control={form.control}
                      name="parentRoleIds"
                      render={({ field }) => (
                        <FormItem>
                          <LabelTooltip label="Parent Roles" />
                          <FormControl>
                            <MultiSelectNew
                              onValueChange={field.onChange}
                              options={roles ?? []}
                              defaultValue={field.value}
                            />
                          </FormControl>
                          <FieldInfo>
                            <FormMessage />
                          </FieldInfo>
                        </FormItem>
                      )}
                    />
                  </TabsContent>
                </Tabs>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-2">
              <Button
                type="submit"
                disabled={
                  status === 'pending' || (!isDirty && !initialData) || !isValid
                }
              >
                {status === 'pending'
                  ? 'Saving...'
                  : initialData
                    ? 'Update Role'
                    : 'Create Role'}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>
    );
  }
}
