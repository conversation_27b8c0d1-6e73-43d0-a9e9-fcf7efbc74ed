import { NewPermission } from '@/app/[locale]/admin/permissions/ajouter/new-permission';
import { redirect } from '@/lib/navigation';
import type { BasePageParams } from '@/types/common';
import { auth } from '@rie/auth';
import { headers } from 'next/headers';

export default async function PermissionsPage({ params }: BasePageParams) {
  const { locale } = await params;
  const sessionData = await auth.api.getSession({ headers: await headers() });

  if (!sessionData?.user) {
    return redirect({ href: { pathname: '/login' }, locale });
  }

  return (
    <div className="container mx-auto py-10">
      <NewPermission />
    </div>
  );
}
