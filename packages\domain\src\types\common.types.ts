import type { DbSession, DbUser } from '@rie/db-schema/entity-types';

export type NonEmptyArray<T> = [T, ...T[]];

export interface OptionalTranslationArgs {
  maxLengthErrorMessage: string;
}

export interface RequiredTranslationArgs extends OptionalTranslationArgs {
  missingTranslationMessage: string;
}

export interface HonoVariables {
  user: DbUser | null;
  session: DbSession | null;
}

export type I18nRow = {
  locale: string;
  name?: string | null;
  website?: string | null;
  description?: string | null;
  otherNames?: string | null;
};

// Types pour les serializers - données avec traductions jointes
export type CampusWithTranslations = {
  id: string;
  translations?: Array<{
    locale: string;
    name?: string | null;
  }>;
};

export type UnitWithTranslations = {
  id: string;
  translations?: Array<{
    locale: string;
    name?: string | null;
  }>;
};

export type InstitutionWithTranslations = {
  id: string;
  translations?: Array<{
    locale: string;
    name?: string | null;
  }>;
};

export type FundingProjectWithTranslations = {
  id: string;
  translations?: Array<{
    locale: string;
    name?: string | null;
  }>;
};
