import type { Env, Hono, Input, <PERSON><PERSON><PERSON><PERSON><PERSON>, Schema } from 'hono';
import type {
  BlankEnv,
  BlankInput,
  BlankSchema,
  DescribeRouteOptions,
  OpenApiSpecsOptions,
} from 'hono-openapi';

declare module 'hono-openapi' {
  export function openAPISpecs<
    E extends Env = BlankEnv,
    P extends string = string,
    I extends Input = BlankInput,
    S extends Schema = BlankSchema,
  >(
    hono: Hono<E, S, P>,
    {
      documentation,
      excludeStaticFile,
      exclude,
      excludeMethods,
      excludeTags,
    }?: OpenApiSpecsOptions,
  ): MiddlewareHandler<E, P, I>;
  export function describeRoute(
    options: DescribeRouteOptions,
  ): MiddlewareHandler;
}
