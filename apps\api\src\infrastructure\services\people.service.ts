import { PeopleRepositoryLive } from '@/infrastructure/repositories/people.repository';
import type {
  CreatePersonPayload,
  UpdatePersonPayload,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';

export class PeopleServiceLive extends Effect.Service<PeopleServiceLive>()(
  'PeopleServiceLive',
  {
    dependencies: [PeopleRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const peopleRepository = yield* PeopleRepositoryLive;

      const getAllPeople = () => {
        return Effect.gen(function* () {
          return yield* peopleRepository.findAllPeople();
        });
      };

      const getPersonById = (id: string) => {
        return Effect.gen(function* () {
          const person = yield* peopleRepository.findPersonById(id);
          if (!person) {
            return yield* Effect.fail(
              new Error(`Person with id ${id} not found`),
            );
          }
          return person;
        });
      };

      const createPerson = (data: CreatePersonPayload) => {
        return Effect.gen(function* () {
          return yield* peopleRepository.createPerson({
            guidId: data.guidId,
            uid: data.uid,
            firstName: data.firstName,
            lastName: data.lastName,
            userId: data.userId,
            modifiedBy: data.modifiedBy,
          });
        });
      };

      const updatePerson = (params: UpdatePersonPayload) => {
        const { id, ...updateData } = params;
        return Effect.gen(function* () {
          const existingPerson = yield* peopleRepository.findPersonById(id);
          if (!existingPerson) {
            return yield* Effect.fail(
              new Error(`Person with id ${id} not found`),
            );
          }

          return yield* peopleRepository.updatePerson({
            id,
            guidId: updateData.guidId,
            uid: updateData.uid,
            firstName: updateData.firstName,
            lastName: updateData.lastName,
            userId: updateData.userId,
            modifiedBy: updateData.modifiedBy,
          });
        });
      };

      const deletePerson = (id: string) => {
        return Effect.gen(function* () {
          const existingPerson = yield* peopleRepository.findPersonById(id);
          if (!existingPerson) {
            return yield* Effect.fail(
              new Error(`Person with id ${id} not found`),
            );
          }
          return yield* peopleRepository.deletePerson(id);
        });
      };

      return {
        getAllPeople,
        getPersonById,
        createPerson,
        updatePerson,
        deletePerson,
      } as const;
    }),
  },
) {}
