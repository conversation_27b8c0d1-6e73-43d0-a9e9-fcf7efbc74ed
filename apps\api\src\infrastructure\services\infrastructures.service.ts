import { InfrastructuresRepositoryLive } from '@/infrastructure/repositories/infrastructures.repository';
import type {
  CreateInfrastructureSchema,
  UpdateInfrastructureSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

export class InfrastructuresServiceLive extends Effect.Service<InfrastructuresServiceLive>()(
  'InfrastructuresServiceLive',
  {
    dependencies: [InfrastructuresRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const infrastructuresRepository = yield* InfrastructuresRepositoryLive;

      const getAllInfrastructures = () => {
        return Effect.gen(function* () {
          return yield* infrastructuresRepository.findAllInfrastructures();
        });
      };

      const getInfrastructureById = (id: string) => {
        return Effect.gen(function* () {
          const infrastructure =
            yield* infrastructuresRepository.findInfrastructureById(id);
          if (!infrastructure) {
            return yield* Effect.fail(
              new Error(`Infrastructure with id ${id} not found`),
            );
          }
          return infrastructure;
        });
      };

      const createInfrastructure = (
        data: Schema.Schema.Type<typeof CreateInfrastructureSchema>,
      ) => {
        return Effect.gen(function* () {
          return yield* infrastructuresRepository.createInfrastructureWithTranslations(
            {
              infrastructure: {
                guidId: data.guidId,
                typeId: data.typeId,
                addressId: data.addressId,
                statusId: data.statusId,
                website: data.website,
                is_featured: data.is_featured,
                visibilityId: data.visibilityId,
                modifiedBy: data.modifiedBy,
              },
              translations: data.translations ? [...data.translations] : [],
            },
          );
        });
      };

      const updateInfrastructure = (
        params: { id: string } & Schema.Schema.Type<
          typeof UpdateInfrastructureSchema
        >,
      ) => {
        const { id, ...updateData } = params;
        return Effect.gen(function* () {
          const existingInfrastructure =
            yield* infrastructuresRepository.findInfrastructureById(id);
          if (!existingInfrastructure) {
            return yield* Effect.fail(
              new Error(`Infrastructure with id ${id} not found`),
            );
          }

          // Update the infrastructure with translations atomically
          const updatedInfrastructure =
            yield* infrastructuresRepository.updateInfrastructureWithTranslations(
              {
                id: id,
                infrastructure: {
                  guidId: updateData.guidId,
                  typeId: updateData.typeId,
                  addressId: updateData.addressId,
                  statusId: updateData.statusId,
                  website: updateData.website,
                  is_featured: updateData.is_featured,
                  visibilityId: updateData.visibilityId,
                  modifiedBy: updateData.modifiedBy,
                },
                translations: updateData.translations
                  ? [...updateData.translations]
                  : [],
              },
            );

          return updatedInfrastructure;
        });
      };

      const deleteInfrastructure = (id: string) => {
        return Effect.gen(function* () {
          const existingInfrastructure =
            yield* infrastructuresRepository.findInfrastructureById(id);
          if (!existingInfrastructure) {
            return yield* Effect.fail(
              new Error(`Infrastructure with id ${id} not found`),
            );
          }
          return yield* infrastructuresRepository.deleteInfrastructure(id);
        });
      };

      return {
        getAllInfrastructures,
        getInfrastructureById,
        createInfrastructure,
        updateInfrastructure,
        deleteInfrastructure,
      } as const;
    }),
  },
) {}
