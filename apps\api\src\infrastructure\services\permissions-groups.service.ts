import { PermissionsGroupsRepositoryLive } from '@/infrastructure/repositories/permissions-groups.repository';
import type { DbPermissionGroupInput } from '@rie/db-schema/entity-types';
import {
  PermissionGroupAlreadyExistsError,
  PermissionGroupNotFoundError,
} from '@rie/domain/errors';
import { Effect } from 'effect';

export class PermissionsGroupsServiceLive extends Effect.Service<PermissionsGroupsServiceLive>()(
  'PermissionGroupsServiceLive',
  {
    dependencies: [PermissionsGroupsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      /**
       * Get all permission groups
       */
      const getAllPermissionsGroups = () => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionsGroupsRepository =
            yield* PermissionsGroupsRepositoryLive;
          const permissionGroups =
            yield* permissionsGroupsRepository.findAllPermissionsGroups();
          const t1 = performance.now();

          console.log(
            `Call to getAllPermissionGroups took ${t1 - t0} milliseconds.`,
          );

          return permissionGroups;
        });
      };

      /**
       * Get a permission group by ID with all its permissions
       */
      const getPermissionsGroupById = (id: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionsGroupsRepository =
            yield* PermissionsGroupsRepositoryLive;
          const permissionGroup =
            yield* permissionsGroupsRepository.findPermissionsGroupById(id);

          if (!permissionGroup) {
            return yield* Effect.fail(new PermissionGroupNotFoundError({ id }));
          }

          const t1 = performance.now();
          console.log(
            `Call to getPermissionGroupById took ${t1 - t0} milliseconds.`,
          );

          return permissionGroup;
        });
      };

      /**
       * Create a new permission group with optional permissions
       */
      interface CreatePermissionsGroupParams {
        name: string;
        description?: string;
        permissionIds: readonly string[];
      }

      const createPermissionsGroup = ({
        name,
        description,
        permissionIds,
      }: CreatePermissionsGroupParams) => {
        return Effect.gen(function* () {
          const permissionGroupsRepository =
            yield* PermissionsGroupsRepositoryLive;

          // Check if permission group already exists
          const existingGroup =
            yield* permissionGroupsRepository.checkPermissionsGroupExists(name);

          if (existingGroup) {
            return yield* Effect.fail(
              new PermissionGroupAlreadyExistsError({ name }),
            );
          }

          const groupData: DbPermissionGroupInput = {
            name,
            description,
          };

          return yield* permissionGroupsRepository.createPermissionsGroup({
            groupData,
            permissionIds,
          });
        });
      };

      /**
       * Update a permission group
       */

      interface UpdatePermissionsGroupParams
        extends CreatePermissionsGroupParams {
        id: string;
      }

      const updatePermissionsGroup = ({
        id,
        name,
        description,
        permissionIds,
      }: UpdatePermissionsGroupParams) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionGroupsRepository =
            yield* PermissionsGroupsRepositoryLive;

          // Check if permission group exists
          const permissionGroup =
            yield* permissionGroupsRepository.findPermissionsGroupById(id);
          if (!permissionGroup) {
            return yield* Effect.fail(new PermissionGroupNotFoundError({ id }));
          }

          // Check if another permission group with the same name already exists
          const existingGroup =
            yield* permissionGroupsRepository.checkPermissionsGroupExists(name);
          if (existingGroup && existingGroup.id !== id) {
            return yield* Effect.fail(
              new PermissionGroupAlreadyExistsError({ name }),
            );
          }

          const updatedPermissionGroup =
            yield* permissionGroupsRepository.updatePermissionsGroup({
              id,
              groupData: { name, description },
              permissionIds,
            });
          const t1 = performance.now();

          console.log(
            `Call to updatePermissionGroup took ${t1 - t0} milliseconds.`,
          );

          return updatedPermissionGroup;
        });
      };

      /**
       * Delete a permission group
       */
      const deletePermissionsGroup = (id: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionGroupsRepository =
            yield* PermissionsGroupsRepositoryLive;

          // Check if permission group exists
          const permissionGroup =
            yield* permissionGroupsRepository.findPermissionsGroupById(id);
          if (!permissionGroup) {
            return yield* Effect.fail(new PermissionGroupNotFoundError({ id }));
          }

          const result =
            yield* permissionGroupsRepository.deletePermissionsGroup(id);
          const t1 = performance.now();

          console.log(
            `Call to deletePermissionGroup took ${t1 - t0} milliseconds.`,
          );

          return result.length > 0;
        });
      };

      return {
        getAllPermissionsGroups,
        getPermissionsGroupById,
        createPermissionsGroup,
        updatePermissionsGroup,
        deletePermissionsGroup,
      } as const;
    }),
  },
) {}
