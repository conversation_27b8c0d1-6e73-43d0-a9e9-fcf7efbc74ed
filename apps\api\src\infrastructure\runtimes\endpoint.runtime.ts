import { ConfigLive } from '@/infrastructure/config/config.live';
import { LocaleRepositoryLive } from '@/infrastructure/repositories/locale.repository';
import { ServiceOfferRepositoryLive } from '@/infrastructure/repositories/service-offer.repository';
import { ServiceOfferServiceLive } from '@/infrastructure/services/service-offer.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import { Effect } from 'effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const ServicesLayer = Layer.mergeAll(
  ServiceOfferRepositoryLive.Default,
  LocaleRepositoryLive.Default,
  ServiceOfferServiceLive.Default,
);

// Create a combined database layer

export const EndpointRuntime = ManagedRuntime.make(
  Layer.provide(ServicesLayer, Layer.merge(ConfigLive.Default, PgDatabaseLive)),
);
