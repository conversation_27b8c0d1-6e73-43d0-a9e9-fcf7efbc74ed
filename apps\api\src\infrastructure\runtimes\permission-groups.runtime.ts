import { ConfigLive } from '@/infrastructure/config/config.live';
import { PermissionsGroupsRepositoryLive } from '@/infrastructure/repositories/permissions-groups.repository';
import { PermissionsGroupsServiceLive } from '@/infrastructure/services/permissions-groups.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import { Effect } from 'effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const PermissionGroupServicesLayer = Layer.mergeAll(
  PermissionsGroupsRepositoryLive.Default,
  PermissionsGroupsServiceLive.Default,
);

export const PermissionGroupsRuntime = ManagedRuntime.make(
  Layer.provide(
    PermissionGroupServicesLayer,
    Layer.merge(ConfigLive.Default, PgDatabaseLive),
  ),
);
