import { ConfigLive } from '@/infrastructure/config/config.live';
import { locales } from '@rie/db-schema/schemas';
import { Database } from '@rie/postgres-db';
import { Database as PgDatabase } from '@rie/postgres-db';
import { eq, not } from 'drizzle-orm';
import { Cache, Duration, Array as EArray, Effect } from 'effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class LocaleRepositoryLive extends Effect.Service<LocaleRepositoryLive>()(
  'LocaleRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;
      const getFallbackLocale = (locale: string) => {
        const cacheKey = `fallback-locale-${locale}`;

        const queryEffect = (locale: string) =>
          dbClient
            .execute((db) =>
              db.query.locales.findMany({
                where: not(eq(locales.code, locale)),
                columns: {
                  code: true,
                },
              }),
            )
            .pipe(
              Effect.flatMap(EArray.head),
              Effect.map(({ code }) => code),
            );

        return Effect.gen(function* (_) {
          const cache = yield* Cache.make({
            capacity: 10,
            timeToLive: Duration.infinity,
            lookup: queryEffect,
          });

          return yield* cache.get(cacheKey);
        });
      };

      return { getFallbackLocale } as const;
    }),
  },
) {}
