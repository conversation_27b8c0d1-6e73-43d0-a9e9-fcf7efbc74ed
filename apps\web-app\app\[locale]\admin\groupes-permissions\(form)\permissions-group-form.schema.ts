import {
  createOptionalStringOfMaxLengthSchema,
  createRequiredStringOfMaxLengthSchema,
} from '@rie/domain/schemas';
import * as Schema from 'effect/Schema';

const baseDescriptionSchema = createOptionalStringOfMaxLengthSchema({
  fieldMaxLength: 1500,
  maxLengthErrorMessage: (issue) =>
    `Description must be ${issue._tag.length} characters or less`,
});

export const permissionsGroupFormSchema = Schema.Struct({
  name: createRequiredStringOfMaxLengthSchema({
    fieldMaxLength: 20,
    errorMessages: {
      required: () => 'Name is required',
      maxLength: (issue) =>
        `Name must be ${issue._tag.length} characters or less`,
    },
  }),
  description: baseDescriptionSchema,
  permissionIds: Schema.Array(Schema.String).pipe(
    Schema.minItems(1, {
      message: () => 'At least one permission is required',
    }),
  ),
});

export type PermissionsGroupFormSchema = Schema.Schema.Type<
  typeof permissionsGroupFormSchema
>;
