import {
  createOptionalStringOfMaxLengthSchema,
  createRequiredStringOfMaxLengthSchema,
} from '@rie/domain/schemas';
import * as Schema from 'effect/Schema';

export const permissionFormSchema = Schema.Struct({
  domain: createRequiredStringOfMaxLengthSchema({
    fieldMaxLength: 10,
    errorMessages: {
      required: () => 'Domain is required',
      maxLength: (issue) =>
        `Domain must be ${issue._tag.length} characters or less`,
    },
  }),
  action: createRequiredStringOfMaxLengthSchema({
    fieldMaxLength: 10,
    errorMessages: {
      required: () => 'Action is required',
      maxLength: (issue) =>
        `Action must be ${issue._tag.length} characters or less`,
    },
  }),
  description: createOptionalStringOfMaxLengthSchema({
    fieldMaxLength: 1500,
    maxLengthErrorMessage: (issue) =>
      `Description must be ${issue._tag.length} characters or less`,
  }),
});

export type PermissionFormSchema = Schema.Schema.Type<
  typeof permissionFormSchema
>;
