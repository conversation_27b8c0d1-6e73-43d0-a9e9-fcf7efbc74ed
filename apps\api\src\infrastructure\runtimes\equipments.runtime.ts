import { ConfigLive } from '@/infrastructure/config/config.live';
import { EquipmentsRepositoryLive } from '@/infrastructure/repositories/equipments.repository';
import { EquipmentsServiceLive } from '@/infrastructure/services/equipments.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      PgDatabase.pgLayer({ url: env.PG_DATABASE_URL, ssl: env.ENV === 'prod' }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const EquipmentsServicesLayer = Layer.mergeAll(
  EquipmentsRepositoryLive.Default,
  EquipmentsServiceLive.Default,
);

export const EquipmentsRuntime = ManagedRuntime.make(
  Layer.provide(
    EquipmentsServicesLayer,
    Layer.merge(ConfigLive.Default, PgDatabaseLive),
  ),
);
