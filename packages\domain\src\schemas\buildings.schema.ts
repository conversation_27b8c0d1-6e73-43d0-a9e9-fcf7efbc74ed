import type { DbBuilding, DbBuildingInput } from '@rie/db-schema/entity-types';
import * as Schema from 'effect/Schema';

// — Translation sub‐object
export const BuildingTranslationSchema = Schema.Struct({
  locale: Schema.String, // e.g. "en"
  name: Schema.String, // required name
  description: Schema.optional(Schema.String), // Optional
  otherNames: Schema.optional(Schema.String), // Optional
  acronyms: Schema.optional(Schema.String), // Optional
});

// — Full Building shape
export const BuildingSchema = Schema.Struct({
  id: Schema.String, // cuid
  campusId: Schema.optional(Schema.String), // nullable
  civicAddressId: Schema.optional(Schema.String), // nullable
  sadId: Schema.optional(Schema.String), // nullable
  diId: Schema.optional(Schema.String), // nullable
  translations: Schema.Array(BuildingTranslationSchema), // at least []
  createdAt: Schema.String, // ISO timestamp
  updatedAt: Schema.String, // ISO timestamp
});

// — Input (create/update) shape
export const BuildingInputSchema = Schema.Struct({
  campusId: Schema.optional(Schema.String),
  civicAddressId: Schema.optional(Schema.String),
  sadId: Schema.optional(Schema.String),
  diId: Schema.optional(Schema.String),
  translations: Schema.optional(Schema.Array(BuildingTranslationSchema)),
});

export type Building = DbBuilding;
export type CreateBuildingPayload = DbBuildingInput;
export type UpdateBuildingPayload = DbBuildingInput & { id: string };
