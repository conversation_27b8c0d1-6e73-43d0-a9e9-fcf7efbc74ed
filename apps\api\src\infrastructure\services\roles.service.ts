import { RolesRepositoryLive } from '@/infrastructure/repositories/roles.repository';
import type { DbRoleInput } from '@rie/db-schema/entity-types';
import { RoleAlreadyExistsError, RoleNotFoundError } from '@rie/domain/errors';
import * as Effect from 'effect/Effect';

export class RolesServiceLive extends Effect.Service<RolesServiceLive>()(
  'RolesServiceLive',
  {
    dependencies: [RolesRepositoryLive.Default],
    effect: Effect.gen(function* () {
      /**
       * Get all roles
       */
      const getAllRoles = () => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;
          const roles = yield* rolesRepository.findAllRoles();
          const t1 = performance.now();

          console.log(`Call to getAllRoles took ${t1 - t0} milliseconds.`);

          return roles;
        });
      };

      /**
       * Get a role by ID with all its relationships
       */
      const getRoleById = (id: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;
          const role = yield* rolesRepository.findRoleById(id);

          if (!role) {
            return yield* Effect.fail(new RoleNotFoundError({ id }));
          }

          const t1 = performance.now();
          console.log(`Call to getRoleById took ${t1 - t0} milliseconds.`);

          return role;
        });
      };

      /**
       * Create a new role with optional direct permissions, parent roles, and permission groups
       */

      interface RoleInputParams {
        name: string;
        description?: string;
        directPermissionIds: readonly string[];
        parentRoleIds: readonly string[];
        permissionGroupIds: readonly string[];
      }

      const createRole = ({
        name,
        description,
        directPermissionIds,
        parentRoleIds,
        permissionGroupIds,
      }: RoleInputParams) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;

          // Check if role already exists
          const existingRole = yield* rolesRepository.checkRoleExists(name);

          if (existingRole) {
            return yield* Effect.fail(new RoleAlreadyExistsError({ name }));
          }

          const roleData: DbRoleInput = {
            name,
            description,
          };

          const role = yield* rolesRepository.createRole({
            roleData,
            directPermissionIds,
            parentRoleIds,
            permissionGroupIds,
          });

          const t1 = performance.now();
          console.log(`Call to createRole took ${t1 - t0} milliseconds.`);

          return role;
        });
      };

      /**
       * Update a role's name
       */

      interface UpdateRoleParams extends RoleInputParams {
        id: string;
      }

      const updateRole = ({
        id,
        name,
        description,
        directPermissionIds,
        parentRoleIds,
        permissionGroupIds,
      }: UpdateRoleParams) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;

          // Check if role exists
          const role = yield* rolesRepository.findRoleById(id);
          if (!role) {
            return yield* Effect.fail(new RoleNotFoundError({ id }));
          }

          // Check if another role with the same name already exists
          const existingRole = yield* rolesRepository.checkRoleExists(name);
          if (existingRole && existingRole.id !== id) {
            return yield* Effect.fail(new RoleAlreadyExistsError({ name }));
          }

          const updatedRole = yield* rolesRepository.updateRole({
            id,
            roleData: { name, description },
            directPermissionIds,
            parentRoleIds,
            permissionGroupIds,
          });
          const t1 = performance.now();

          console.log(`Call to updateRole took ${t1 - t0} milliseconds.`);

          return updatedRole;
        });
      };

      /**
       * Delete a role
       */
      const deleteRole = (id: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;

          // Check if role exists
          const role = yield* rolesRepository.findRoleById(id);
          if (!role) {
            return yield* Effect.fail(new RoleNotFoundError({ id }));
          }

          const result = yield* rolesRepository.deleteRole(id);
          const t1 = performance.now();

          console.log(`Call to deleteRole took ${t1 - t0} milliseconds.`);

          return result.length > 0;
        });
      };

      /**
       * Get all permissions for a role (direct, inherited, and from groups)
       */
      const getRolePermissions = (roleId: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;

          // Check if role exists
          const role = yield* rolesRepository.findRoleById(roleId);
          if (!role) {
            return yield* Effect.fail(new Error('Role not found'));
          }

          const roleWithPermissions =
            yield* rolesRepository.getRolePermissions(roleId);
          const t1 = performance.now();

          console.log(
            `Call to getRolePermissions took ${t1 - t0} milliseconds.`,
          );

          return roleWithPermissions;
        });
      };

      /**
       * Get all permission groups for a role
       */
      const getRolePermissionGroups = (roleId: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const rolesRepository = yield* RolesRepositoryLive;

          // Check if role exists
          const role = yield* rolesRepository.findRoleById(roleId);
          if (!role) {
            return yield* Effect.fail(new Error('Role not found'));
          }

          const permissionGroups =
            yield* rolesRepository.getRolePermissionGroups(roleId);
          const t1 = performance.now();

          console.log(
            `Call to getRolePermissionGroups took ${t1 - t0} milliseconds.`,
          );

          return permissionGroups;
        });
      };

      return {
        getAllRoles,
        getRoleById,
        createRole,
        updateRole,
        deleteRole,
        getRolePermissions,
        getRolePermissionGroups,
      } as const;
    }),
  },
) {}
