import type { Db<PERSON><PERSON><PERSON>, DbVendorInput } from '@rie/db-schema/entity-types';
import * as Schema from 'effect/Schema';

// — Translation sub‐object
export const VendorTranslationSchema = Schema.Struct({
  locale: Schema.String, // e.g. "en"
  name: Schema.optional(Schema.String), // vendor name
  website: Schema.optional(Schema.String), // vendor website
  description: Schema.optional(Schema.String), // Optional description
  otherNames: Schema.optional(Schema.String), // Optional
});

// — Full Vendor shape
export const VendorSchema = Schema.Struct({
  id: Schema.String, // cuid
  startDate: Schema.optional(Schema.String), // start date of vendor relationship
  endDate: Schema.optional(Schema.String), // end date of vendor relationship
  translations: Schema.Array(VendorTranslationSchema), // at least []
  createdAt: Schema.String, // ISO timestamp
  updatedAt: Schema.String, // ISO timestamp
  modifiedBy: Schema.optional(Schema.String), // user ID
});

// — Input (create) shape
export const CreateVendorSchema = Schema.Struct({
  startDate: Schema.String, // start date required for creation
  endDate: Schema.String, // end date required for creation
  translations: Schema.optional(Schema.Array(VendorTranslationSchema)),
  modifiedBy: Schema.optional(Schema.String),
});

// — Input (update) shape
export const UpdateVendorSchema = Schema.Struct({
  startDate: Schema.optional(Schema.String),
  endDate: Schema.optional(Schema.String),
  translations: Schema.optional(Schema.Array(VendorTranslationSchema)),
  modifiedBy: Schema.optional(Schema.String),
});

// — Response schema
export const VendorResponseSchema = VendorSchema;

export type Vendor = DbVendor;
export type CreateVendorPayload = DbVendorInput;
export type UpdateVendorPayload = DbVendorInput & { id: string };
