'use client';

import { PermissionsGroupForm } from '@/app/[locale]/admin/groupes-permissions/(form)/permissions-group-form';
import type { PermissionsGroupFormSchema } from '@/app/[locale]/admin/groupes-permissions/(form)/permissions-group-form.schema';
import { useCreatePermissionsGroup } from '@/hooks/admin/permissions/permissions-groups.hook';

export const NewPermissionsGroup = () => {
  const { mutate, status } = useCreatePermissionsGroup();

  const handleSubmit = (data: PermissionsGroupFormSchema) => {
    mutate(data);
  };

  return <PermissionsGroupForm onSubmit={handleSubmit} status={status} />;
};
