import { handleEffectError } from '@/api/v2/utils/error-handler';
import { UnitsRuntime } from '@/infrastructure/runtimes/units.runtime';
import { UnitsServiceLive } from '@/infrastructure/services/units.service';
import { effectValidator } from '@hono/effect-validator';
import { CreateUnitSchema, UpdateUnitSchema } from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// Import additional schemas
import { ResourceIdSchema, UnitSchema } from '@rie/domain/schemas';

// OpenAPI route descriptions
export const getAllUnitsRoute = describeRoute({
  description: 'Lister toutes les unités',
  operationId: 'getAllUnits',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(UnitSchema)),
        },
      },
      description: 'Unités retournées',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

export const getUnitByIdRoute = describeRoute({
  description: 'Obtenir une unité par ID',
  operationId: 'getUnitById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'unité (format CUID)",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(UnitSchema),
        },
      },
      description: 'Unité trouvée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Unité non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

export const createUnitRoute = describeRoute({
  description: 'Créer une unité',
  operationId: 'createUnit',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(CreateUnitSchema),
        example: {
          buildingId: 'bw6c4rifj8239sc6gheruphi',
          sadId: 'UNIT_001',
          translations: [
            {
              locale: 'en',
              name: 'Laboratory Unit A',
              description: 'Advanced research laboratory unit',
            },
            {
              locale: 'fr',
              name: 'Unité de Laboratoire A',
              description: 'Unité de laboratoire de recherche avancée',
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(UnitSchema),
        },
      },
      description: 'Unité créée',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

export const updateUnitRoute = describeRoute({
  description: 'Mettre à jour une unité',
  operationId: 'updateUnit',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'unité (format CUID)",
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(UpdateUnitSchema),
        example: {
          buildingId: 'bw6c4rifj8239sc6gheruphi',
          sadId: 'UPDATED_UNIT_001',
          translations: [
            {
              locale: 'en',
              name: 'Updated Laboratory Unit A',
              description: 'Updated advanced research laboratory unit',
            },
            {
              locale: 'fr',
              name: 'Unité de Laboratoire A Mise à Jour',
              description:
                'Unité de laboratoire de recherche avancée mise à jour',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(UnitSchema),
        },
      },
      description: 'Unité mise à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Unité non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

export const deleteUnitRoute = describeRoute({
  description: 'Supprimer une unité',
  operationId: 'deleteUnit',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'unité (format CUID)",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Unité supprimée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Unité non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

const unitsRoute = new Hono();

unitsRoute.get('/', getAllUnitsRoute, async (ctx) => {
  const program = Effect.gen(function* () {
    const unitService = yield* UnitsServiceLive;
    return yield* unitService.getAllUnits();
  });
  const result = await UnitsRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }
  if (Exit.isSuccess(result)) {
    return ctx.json(result.value);
  }
  return ctx.json({ error: 'An error occurred' }, 500);
});

unitsRoute.get(
  '/:id',
  getUnitByIdRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.getUnitById(id);
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

unitsRoute.post(
  '/',
  createUnitRoute,
  effectValidator('json', CreateUnitSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.createUnit(body);
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

unitsRoute.put(
  '/:id',
  updateUnitRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  effectValidator('json', UpdateUnitSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.updateUnit({ id, ...body });
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

unitsRoute.delete(
  '/:id',
  deleteUnitRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.deleteUnit(id);
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Unit deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { unitsRoute };
