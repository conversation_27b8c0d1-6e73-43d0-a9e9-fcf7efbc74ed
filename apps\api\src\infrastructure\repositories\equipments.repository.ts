import { ConfigLive } from '@/infrastructure/config/config.live';
import { equipments } from '@rie/db-schema/schemas';
import { Database, Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class EquipmentsRepositoryLive extends Effect.Service<EquipmentsRepositoryLive>()(
  'EquipmentsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;

      /**
       * Find all equipments under a specific infrastructure
       */
      const findEquipmentsByInfrastructureId = dbClient.makeQuery(
        (execute, infrastructureId: string) => {
          return execute((client) =>
            client
              .select({
                id: equipments.id,
                guidId: equipments.guidId,
                campusAddressId: equipments.campusAddressId,
                isCampusAddressConfidential:
                  equipments.isCampusAddressConfidential,
                model: equipments.model,
                serialNumber: equipments.serialNumber,
                homologationNumber: equipments.homologationNumber,
                inventoryNumber: equipments.inventoryNumber,
                doi: equipments.doi,
                useInClinicalTrial: equipments.useInClinicalTrial,
                isHidden: equipments.isHidden,
                typeId: equipments.typeId,
                statusId: equipments.statusId,
                workingPercentage: equipments.workingPercentage,
                monetaryCost: equipments.monetaryCost,
                inKindCost: equipments.inKindCost,
                manufactureYear: equipments.manufactureYear,
                acquisitionDate: equipments.acquisitionDate,
                installationDate: equipments.installationDate,
                decommissioningDate: equipments.decommissioningDate,
                scientificManagerId: equipments.scientificManagerId,
                manufacturerId: equipments.manufacturerId,
                supplierId: equipments.supplierId,
                infrastructureId: equipments.infrastructureId,
                isFeatured: equipments.isFeatured,
                institutionId: equipments.institutionId,
                createdAt: equipments.createdAt,
                updatedAt: equipments.updatedAt,
                modifiedBy: equipments.modifiedBy,
              })
              .from(equipments)
              .where(eq(equipments.infrastructureId, infrastructureId)),
          );
        },
      );

      /**
       * Find all equipments under a specific infrastructure with translations and related data
       */
      const findEquipmentsByInfrastructureIdWithTranslations =
        dbClient.makeQuery((execute, infrastructureId: string) => {
          return execute((client) =>
            client.query.equipments.findMany({
              where: eq(equipments.infrastructureId, infrastructureId),
              columns: {
                id: true,
                guidId: true,
                campusAddressId: true,
                isCampusAddressConfidential: true,
                model: true,
                serialNumber: true,
                homologationNumber: true,
                inventoryNumber: true,
                doi: true,
                useInClinicalTrial: true,
                isHidden: true,
                typeId: true,
                statusId: true,
                workingPercentage: true,
                monetaryCost: true,
                inKindCost: true,
                manufactureYear: true,
                acquisitionDate: true,
                installationDate: true,
                decommissioningDate: true,
                scientificManagerId: true,
                manufacturerId: true,
                supplierId: true,
                infrastructureId: true,
                isFeatured: true,
                institutionId: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    locale: true,
                    name: true,
                    description: true,
                  },
                },
                type: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
                status: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
                scientificManager: {
                  columns: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    uid: true,
                  },
                },
                manufacturer: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
                supplier: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
              },
            }),
          );
        });

      /**
       * Find equipment by ID
       */
      const findEquipmentById = dbClient.makeQuery(
        (execute, equipmentId: string) => {
          return execute((client) =>
            client.query.equipments.findFirst({
              where: eq(equipments.id, equipmentId),
              columns: {
                id: true,
                guidId: true,
                campusAddressId: true,
                isCampusAddressConfidential: true,
                model: true,
                serialNumber: true,
                homologationNumber: true,
                inventoryNumber: true,
                doi: true,
                useInClinicalTrial: true,
                isHidden: true,
                typeId: true,
                statusId: true,
                workingPercentage: true,
                monetaryCost: true,
                inKindCost: true,
                manufactureYear: true,
                acquisitionDate: true,
                installationDate: true,
                decommissioningDate: true,
                scientificManagerId: true,
                manufacturerId: true,
                supplierId: true,
                infrastructureId: true,
                isFeatured: true,
                institutionId: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    locale: true,
                    name: true,
                    description: true,
                  },
                },
                type: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
                status: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
                scientificManager: {
                  columns: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    uid: true,
                  },
                },
                manufacturer: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
                supplier: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
                infrastructure: {
                  columns: {
                    id: true,
                    guidId: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
              },
            }),
          );
        },
      );

      return {
        findEquipmentsByInfrastructureId,
        findEquipmentsByInfrastructureIdWithTranslations,
        findEquipmentById,
      } as const;
    }),
  },
) { }
