import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { institutionAssociatedUnits, units } from '@rie/db-schema/schemas';
import { Database, Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class UnitsRepositoryLive extends Effect.Service<UnitsRepositoryLive>()(
  'UnitsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* ($) {
      const dbClient = yield* $(Database.PgDatabase);

      // — Fetch all units with translations
      const findAllUnits = dbClient.makeQuery((exec) =>
        exec((client) =>
          client.query.units.findMany({
            columns: {
              id: true,
              guidId: true,
              typeId: true,
              parentId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  dataId: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                  acronyms: true,
                },
              },
            },
          }),
        ),
      );

      // — Fetch one unit by ID
      const findUnitById = dbClient.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.units.findFirst({
            where: eq(DBSchema.units.id, id),
            columns: {
              id: true,
              guidId: true,
              typeId: true,
              parentId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  dataId: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                  acronyms: true,
                },
              },
              type: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
            },
          }),
        ),
      );

      const createUnit = (params: {
        unit: {
          guidId: string;
          typeId: string;
          parentId?: string | null;
        };
        translations: Array<{
          locale: string;
          name?: string | null;
          description?: string | null;
          otherNames?: string | null;
          acronyms?: string | null;
        }>;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            const [u] = yield* tx((client) =>
              client
                .insert(DBSchema.units)
                .values({
                  guidId: params.unit.guidId,
                  typeId: params.unit.typeId,
                  parentId: params.unit.parentId,
                })
                .returning({ id: DBSchema.units.id }),
            );

            if (params.translations.length > 0) {
              yield* tx((client) =>
                client.insert(DBSchema.unitsI18N).values(
                  params.translations.map((t) => ({
                    dataId: u.id,
                    locale: t.locale,
                    name: t.name,
                    description: t.description,
                    otherNames: t.otherNames,
                    acronyms: t.acronyms,
                  })),
                ),
              );
            }

            return u;
          });
        });
      };

      /**
       * Find all units under a specific institution
       */
      const findUnitsByInstitutionId = dbClient.makeQuery(
        (execute, institutionId: string) => {
          return execute((client) =>
            client
              .select({
                id: units.id,
                guidId: units.guidId,
                typeId: units.typeId,
                parentId: units.parentId,
                createdAt: units.createdAt,
                updatedAt: units.updatedAt,
                modifiedBy: units.modifiedBy,
              })
              .from(units)
              .innerJoin(
                institutionAssociatedUnits,
                eq(units.id, institutionAssociatedUnits.unitId),
              )
              .where(
                eq(institutionAssociatedUnits.institutionId, institutionId),
              ),
          );
        },
      );

      /**
       * Find all units under a specific institution with translations
       */
      const findUnitsByInstitutionIdWithTranslations = dbClient.makeQuery(
        (execute, institutionId: string) => {
          return execute((client) =>
            client.query.institutionAssociatedUnits.findMany({
              where: eq(
                institutionAssociatedUnits.institutionId,
                institutionId,
              ),
              with: {
                unit: {
                  columns: {
                    id: true,
                    guidId: true,
                    typeId: true,
                    parentId: true,
                    createdAt: true,
                    updatedAt: true,
                    modifiedBy: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        id: true,
                        locale: true,
                        name: true,
                        description: true,
                        otherNames: true,
                        acronyms: true,
                      },
                    },
                    type: {
                      columns: {
                        id: true,
                      },
                      with: {
                        translations: {
                          columns: {
                            locale: true,
                            name: true,
                            description: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            }),
          );
        },
      );

      const updateUnit = (params: {
        id: string;
        guidId?: string;
        typeId?: string;
        parentId?: string | null;
        modifiedBy: string;
        translations?: Array<{
          locale: string;
          name?: string | null;
          description?: string | null;
          otherNames?: string | null;
          acronyms?: string | null;
        }>;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Update the unit
            const [unit] = yield* tx((client) =>
              client
                .update(DBSchema.units)
                .set({
                  guidId: params.guidId,
                  typeId: params.typeId,
                  parentId: params.parentId,
                  modifiedBy: params.modifiedBy,
                })
                .where(eq(DBSchema.units.id, params.id))
                .returning({
                  id: DBSchema.units.id,
                  guidId: DBSchema.units.guidId,
                  typeId: DBSchema.units.typeId,
                  parentId: DBSchema.units.parentId,
                  createdAt: DBSchema.units.createdAt,
                  updatedAt: DBSchema.units.updatedAt,
                  modifiedBy: DBSchema.units.modifiedBy,
                }),
            );

            // Update translations if provided
            if (params.translations && params.translations.length > 0) {
              const translations = params.translations;
              // Delete existing translations
              yield* tx((client) =>
                client
                  .delete(DBSchema.unitsI18N)
                  .where(eq(DBSchema.unitsI18N.dataId, params.id)),
              );

              // Insert new translations
              yield* tx((client) =>
                client.insert(DBSchema.unitsI18N).values(
                  translations.map((t) => ({
                    dataId: params.id,
                    locale: t.locale,
                    name: t.name,
                    description: t.description,
                    otherNames: t.otherNames,
                    acronyms: t.acronyms,
                  })),
                ),
              );
            }

            return unit;
          });
        });
      };

      // — Delete a unit
      const deleteUnit = dbClient.makeQuery((exec, id: string) =>
        exec((client) =>
          client
            .delete(DBSchema.units)
            .where(eq(DBSchema.units.id, id))
            .returning({ id: DBSchema.units.id }),
        ),
      );

      return {
        findUnitsByInstitutionId,
        findUnitsByInstitutionIdWithTranslations,
        findAllUnits,
        findUnitById,
        createUnit,
        updateUnit,
        deleteUnit,
      } as const;
    }),
  },
) { }
