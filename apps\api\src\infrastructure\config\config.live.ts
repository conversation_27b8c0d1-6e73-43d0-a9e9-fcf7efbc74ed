import { Effect } from 'effect';
import * as Config from 'effect/Config';

export class ConfigLive extends Effect.Service<ConfigLive>()('ConfigClient', {
  accessors: true,
  effect: Effect.gen(function* () {
    return {
      NEXT_PUBLIC_ORIGIN_URL: yield* Config.redacted('NEXT_PUBLIC_ORIGIN_URL'),
      NEXT_PUBLIC_API_BASE_URL: yield* Config.redacted(
        'NEXT_PUBLIC_API_BASE_URL',
      ),
      BETTER_AUTH_SECRET: yield* Config.redacted('BETTER_AUTH_SECRET'),
      GITHUB_CLIENT_ID: yield* Config.redacted('GITHUB_CLIENT_ID'),
      GITHUB_CLIENT_SECRET: yield* Config.redacted('GITHUB_CLIENT_SECRET'),
      MICROSOFT_CLIENT_ID: yield* Config.redacted('MICROSOFT_CLIENT_ID'),
      MICROSOFT_CLIENT_SECRET: yield* Config.redacted(
        'MICROSOFT_CLIENT_SECRET',
      ),
      logLevel: 'info',
      PG_DATABASE_URL: yield* Config.redacted('PG_DATABASE_URL'),
      PORT: yield* Config.integer('PORT').pipe(Config.withDefault(4000)),
      dialect: 'postgresql',
      ENV: yield* Config.literal(
        'dev',
        'prod',
        'staging',
      )('ENV').pipe(Config.withDefault('dev')),
    };
  }),
}) {}
