
FROM cenr/nodejs

USER root
RUN apt-get update


ARG USERNAME
ARG USER_UID
ENV USERNAME=${USERNAME}
ENV USER_UID=${USER_UID}

ARG PORT=3000
ARG RIE_AUTH_CLIENT_ID=testclient
ARG RIE_AUTH_CLIENT_SECRET=testpass
ARG NEXT_PUBLIC_BASE_PATH=/public
ARG NEXT_PUBLIC_ORIGIN_URL=http://localhost:${PORT}${NEXT_PUBLIC_BASE_PATH}
ARG NEXT_PUBLIC_RIE_API_URL=https://rie-devel.cen.umontreal.ca/api/
ARG NEXT_PUBLIC_RIE_AUTH_URL=https://rie-devel.cen.umontreal.ca/api/auth
ARG NEXT_PUBLIC_POSTHOG_HOST=${NEXT_PUBLIC_POSTHOG_HOST:-http://_NEXT_PUBLIC_POSTHOG_HOST_}
ARG NEXT_PUBLIC_POSTHOG_KEY=${NEXT_PUBLIC_POSTHOG_KEY:-_NEXT_PUBLIC_POSTHOG_KEY_}


ENV PORT=${PORT}
ENV RIE_AUTH_CLIENT_ID=${RIE_AUTH_CLIENT_ID}
ENV RIE_AUTH_CLIENT_SECRET=${RIE_AUTH_CLIENT_SECRET}
ENV NEXT_PUBLIC_BASE_PATH=${NEXT_PUBLIC_BASE_PATH}
ENV NEXT_PUBLIC_ORIGIN_URL=${NEXT_PUBLIC_ORIGIN_URL}
ENV NEXT_PUBLIC_RIE_API_URL=${NEXT_PUBLIC_RIE_API_URL}
ENV NEXT_PUBLIC_RIE_AUTH_URL=${NEXT_PUBLIC_RIE_AUTH_URL}
ENV NEXT_PUBLIC_POSTHOG_HOST=${NEXT_PUBLIC_POSTHOG_HOST}
ENV NEXT_PUBLIC_POSTHOG_KEY=${NEXT_PUBLIC_POSTHOG_KEY}

ENV NODE_TLS_REJECT_UNAUTHORIZED=0


# Create the user
RUN useradd --uid $USER_UID  -m $USERNAME \
    && echo $USERNAME ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/$USERNAME \
    && chmod 0440 /etc/sudoers.d/$USERNAME

# Set Bash as the default shell for the new user
RUN chsh -s /bin/bash $USERNAME

USER ${USERNAME}

WORKDIR /workspace/projects/rie/rie-frontend-nextjs