{"compilerOptions": {"lib": ["ESNext"], "target": "ESNext", "module": "ESNext", "moduleDetection": "force", "allowJs": true, "types": ["./hono-openapi.d.ts", "bun"], "moduleResolution": "<PERSON><PERSON><PERSON>", "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}}