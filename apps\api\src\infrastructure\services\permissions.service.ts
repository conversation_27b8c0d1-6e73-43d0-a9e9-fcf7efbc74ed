import { PermissionsRepositoryLive } from '@/infrastructure/repositories/permissions.repository';
import {
  PermissionAlreadyExistsError,
  PermissionNotFoundError,
} from '@rie/domain/errors';
import type { PermissionInputSchema } from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

export class PermissionsServiceLive extends Effect.Service<PermissionsServiceLive>()(
  'PermissionsServiceLive',
  {
    dependencies: [PermissionsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllPermissions = () => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionsRepository = yield* PermissionsRepositoryLive;
          const permissions = yield* permissionsRepository.findAllPermissions();
          const t1 = performance.now();

          console.log(
            `Call to getAllPermissions took ${t1 - t0} milliseconds.`,
          );

          return permissions;
        });
      };

      type CreatePermissionParams = Schema.Schema.Type<
        typeof PermissionInputSchema
      >;

      const createPermission = (data: CreatePermissionParams) => {
        return Effect.gen(function* () {
          const permissionsRepository = yield* PermissionsRepositoryLive;

          return yield* permissionsRepository.createPermission(data).pipe(
            Effect.catchTag('DatabaseError', (error) => {
              if (error.type === 'unique_violation') {
                return Effect.fail(
                  new PermissionAlreadyExistsError({
                    domain: data.domain,
                    action: data.action,
                  }),
                );
              }

              return Effect.fail(
                new Error(`Unexpected database error: ${error.message}`),
              );
            }),
          );
        });
      };

      interface UpdatePermissionParams extends CreatePermissionParams {
        id: string;
      }

      const updatePermission = ({
        id,
        action,
        domain,
      }: UpdatePermissionParams) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionsRepository = yield* PermissionsRepositoryLive;

          // Check if another permission with the same domain/action already exists (excluding this one)
          const existingPermission =
            yield* permissionsRepository.checkPermissionExists({
              action,
              domain,
            });

          if (existingPermission && existingPermission.id !== id) {
            return yield* Effect.fail(
              new PermissionAlreadyExistsError({ action, domain }),
            );
          }

          const [permission] = yield* permissionsRepository.updatePermission({
            id,
            action,
            domain,
          });
          const t1 = performance.now();

          console.log(`Call to updatePermission took ${t1 - t0} milliseconds.`);

          return permission;
        });
      };

      const deletePermission = (id: string) => {
        const t0 = performance.now();

        return Effect.gen(function* () {
          const permissionsRepository = yield* PermissionsRepositoryLive;

          const existingPermission =
            yield* permissionsRepository.findPermissionById(id);

          if (!existingPermission) {
            return yield* Effect.fail(new PermissionNotFoundError({ id }));
          }

          const result = yield* permissionsRepository.deletePermission(id);
          const t1 = performance.now();

          console.log(`Call to deletePermission took ${t1 - t0} milliseconds.`);

          return result.length > 0;
        });
      };

      return {
        getAllPermissions,
        createPermission,
        updatePermission,
        deletePermission,
      } as const;
    }),
  },
) {}
