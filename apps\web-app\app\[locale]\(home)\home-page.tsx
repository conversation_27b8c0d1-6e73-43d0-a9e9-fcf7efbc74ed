'use client';

import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useGetAllEquipments } from '@/hooks/equipment/useGetAllEquipments';
import { useGetAllInfrastructures } from '@/hooks/infrastructure/useGetAllInfrastructures';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import { useTranslations } from 'next-intl';
import { GiChemicalTank } from 'react-icons/gi';
import { GiMicroscope } from 'react-icons/gi';

import { ResourceCard } from './resource-card';

export const HomePage = () => {
  const tCommon = useTranslations('common');
  const tHomePage = useTranslations('homePage');

  const locale = useAvailableLocale();
  const { data: infrastructures, status: infrastructureStatus } =
    useGetAllInfrastructures({
      params: { lang: locale, limit: '0' },
      select: (data) => data,
    });
  const { data: equipments, status: equipmentStatus } = useGetAllEquipments({
    params: { lang: locale, limit: '0' },
    select: (data) => data,
  });

  if (infrastructureStatus === 'pending' || equipmentStatus === 'pending') {
    return <LoadingResource />;
  }

  return (
    <div className="flex w-full flex-col items-center gap-y-10 pt-12 md:flex md:flex-row md:items-start md:justify-center md:gap-x-10">
      <ResourceCard
        content={tHomePage('cardContent.infrastructures')}
        href="/infrastructures"
        icon={GiChemicalTank}
        title={`${infrastructures?.count} ${tCommon('infrastructures')}`}
      />
      <ResourceCard
        content={tHomePage('cardContent.equipments')}
        href="/equipements"
        icon={GiMicroscope}
        title={`${equipments?.count} ${tCommon('equipments')}`}
      />
    </div>
  );
};
