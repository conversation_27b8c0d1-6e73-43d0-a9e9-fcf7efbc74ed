import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
} from '@/components/ui/card';
import { Heading } from '@/components/ui/heading';
import type { SideMenuPathnames } from '@/i18n/settings';
import { Link } from '@/lib/navigation';
import { useTranslations } from 'next-intl';
import type { IconType } from 'react-icons';

type ResourceCardProps = {
  content: string;
  href: Extract<SideMenuPathnames, '/equipements' | '/infrastructures'>;
  icon: IconType;
  title: string;
};

export const ResourceCard = (props: ResourceCardProps) => {
  const t = useTranslations('homePage');

  return (
    <Card className="max-w-96">
      <CardHeader>
        <div className="flex items-center gap-x-4">
          <div className="flex items-center justify-center rounded-full bg-blue-300 p-2">
            {<props.icon className="h-8 w-8" />}
          </div>{' '}
          <Heading className="mb-0 text-xl" level={2}>
            {props.title}
          </Heading>
        </div>
      </CardHeader>
      <CardContent>
        <p>{props.content}</p>
      </CardContent>
      <CardFooter>
        <Button>
          <Link className="w-full" href={props.href}>
            {t('takeALook')}
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
};
