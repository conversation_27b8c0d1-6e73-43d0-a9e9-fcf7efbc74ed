import { handleEffectError } from '@/api/v2/utils/error-handler';
import { InfrastructuresRuntime } from '@/infrastructure/runtimes/infrastructures.runtime';
import { InfrastructuresServiceLive } from '@/infrastructure/services/infrastructures.service';
import { effectValidator } from '@hono/effect-validator';
import {
  CreateInfrastructureSchema,
  InfrastructureSchema,
  ResourceIdSchema,
  UpdateInfrastructureSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllInfrastructuresRoute = describeRoute({
  description: 'Lister toutes les infrastructures',
  operationId: 'getAllInfrastructures',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(InfrastructureSchema)),
        },
      },
      description: 'Infrastructures retournées',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Infrastructures'],
});

export const getInfrastructureByIdRoute = describeRoute({
  description: 'Obtenir une infrastructure par ID',
  operationId: 'getInfrastructureById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'infrastructure (format CUID)",
      example: 'infra123abc456def789ghi',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(InfrastructureSchema),
        },
      },
      description: 'Infrastructure trouvée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Infrastructure non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Infrastructures'],
});

export const createInfrastructureRoute = describeRoute({
  description: 'Créer une infrastructure',
  operationId: 'createInfrastructure',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(CreateInfrastructureSchema),
        example: {
          guidId: 'guid_infra_123',
          typeId: 'infra_type_001',
          statusId: 'infra_status_001',
          website: 'https://example-infrastructure.com',
          is_featured: false,
          visibilityId: 'visibility_001',
          translations: [
            {
              locale: 'en',
              name: 'Advanced Research Infrastructure',
              description:
                'State-of-the-art research facility for advanced studies',
              otherNames: 'ARI',
              acronyms: 'ARI',
            },
            {
              locale: 'fr',
              name: 'Infrastructure de Recherche Avancée',
              description:
                'Installation de recherche de pointe pour les études avancées',
              otherNames: 'IRA',
              acronyms: 'IRA',
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(InfrastructureSchema),
        },
      },
      description: 'Infrastructure créée',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Erreur de validation - Données d'entrée invalides ou champs requis manquants",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Clé étrangère non trouvée - Le type, statut ou visibilité n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Infrastructures'],
});

export const updateInfrastructureRoute = describeRoute({
  description: 'Mettre à jour une infrastructure',
  operationId: 'updateInfrastructure',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'infrastructure (format CUID)",
      example: 'infra123abc456def789ghi',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(UpdateInfrastructureSchema),
        example: {
          guidId: 'guid_infra_updated_123',
          typeId: 'infra_type_002',
          statusId: 'infra_status_002',
          website: 'https://updated-infrastructure.com',
          is_featured: true,
          visibilityId: 'visibility_002',
          translations: [
            {
              locale: 'en',
              name: 'Updated Advanced Research Infrastructure',
              description: 'Updated state-of-the-art research facility',
              otherNames: 'Updated ARI',
              acronyms: 'UARI',
            },
            {
              locale: 'fr',
              name: 'Infrastructure de Recherche Avancée Mise à Jour',
              description: 'Installation de recherche de pointe mise à jour',
              otherNames: 'IRA Mise à Jour',
              acronyms: 'IRAMAJ',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(InfrastructureSchema),
        },
      },
      description: 'Infrastructure mise à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Infrastructure non trouvée ou clé étrangère non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Infrastructures'],
});

export const deleteInfrastructureRoute = describeRoute({
  description: 'Supprimer une infrastructure',
  operationId: 'deleteInfrastructure',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'infrastructure (format CUID)",
      example: 'infra123abc456def789ghi',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Infrastructure supprimée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Infrastructure non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Infrastructures'],
});

const infrastructuresRoute = new Hono();

infrastructuresRoute.get('/', getAllInfrastructuresRoute, async (ctx) => {
  const program = Effect.gen(function* () {
    const infrastructureService = yield* InfrastructuresServiceLive;
    return yield* infrastructureService.getAllInfrastructures();
  });
  const result = await InfrastructuresRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }
  if (Exit.isSuccess(result)) {
    return ctx.json(result.value);
  }
  return ctx.json({ error: 'An error occurred' }, 500);
});

infrastructuresRoute.get(
  '/:id',
  getInfrastructureByIdRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const infrastructureService = yield* InfrastructuresServiceLive;
      return yield* infrastructureService.getInfrastructureById(id);
    });
    const result = await InfrastructuresRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

infrastructuresRoute.post(
  '/',
  createInfrastructureRoute,
  effectValidator('json', CreateInfrastructureSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
      const infrastructureService = yield* InfrastructuresServiceLive;
      return yield* infrastructureService.createInfrastructure(body);
    });
    const result = await InfrastructuresRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

infrastructuresRoute.put(
  '/:id',
  updateInfrastructureRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  effectValidator('json', UpdateInfrastructureSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
      const infrastructureService = yield* InfrastructuresServiceLive;
      return yield* infrastructureService.updateInfrastructure({ id, ...body });
    });
    const result = await InfrastructuresRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

infrastructuresRoute.delete(
  '/:id',
  deleteInfrastructureRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const infrastructureService = yield* InfrastructuresServiceLive;
      return yield* infrastructureService.deleteInfrastructure(id);
    });
    const result = await InfrastructuresRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Infrastructure deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { infrastructuresRoute };
