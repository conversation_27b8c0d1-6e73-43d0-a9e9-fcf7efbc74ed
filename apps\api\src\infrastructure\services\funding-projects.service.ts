import { FundingProjectsRepositoryLive } from '@/infrastructure/repositories/funding-projects.repository';
import { FundingProjectNotFoundError } from '@rie/domain/errors';
import type {
  CreateFundingProjectPayload,
  UpdateFundingProjectPayload,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';

export class FundingProjectsServiceLive extends Effect.Service<FundingProjectsServiceLive>()(
  'FundingProjectsServiceLive',
  {
    dependencies: [FundingProjectsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const fundingProjectsRepository = yield* FundingProjectsRepositoryLive;

      const getAllFundingProjects = () => {
        return Effect.gen(function* () {
          return yield* fundingProjectsRepository.findAllFundingProjects();
        });
      };

      const getFundingProjectById = (id: string) => {
        return Effect.gen(function* () {
          const project =
            yield* fundingProjectsRepository.findFundingProjectById(id);
          if (!project) {
            return yield* Effect.fail(new FundingProjectNotFoundError({ id }));
          }
          return project;
        });
      };

      const createFundingProject = (data: CreateFundingProjectPayload) => {
        return Effect.gen(function* () {
          return yield* fundingProjectsRepository.createFundingProjectWithTranslations(
            {
              project: {
                holderId: data.holderId,
                typeId: data.typeId,
                fciId: data.fciId,
                synchroId: data.synchroId,
                obtainingYear: data.obtainingYear,
                endDate: data.endDate,
                modifiedBy: data.modifiedBy,
              },
              translations: data.translations
                ? data.translations
                    .filter((t) => t.name && t.name.trim() !== '')
                    .map((t) => ({
                      locale: t.locale,
                      name: t.name || 'Unnamed Project',
                      description: t.description,
                    }))
                : [],
            },
          );
        });
      };

      const updateFundingProject = (params: UpdateFundingProjectPayload) => {
        const { id, ...updateData } = params;
        return Effect.gen(function* () {
          const existingProject =
            yield* fundingProjectsRepository.findFundingProjectById(id);
          if (!existingProject) {
            return yield* Effect.fail(new FundingProjectNotFoundError({ id }));
          }

          const updated =
            yield* fundingProjectsRepository.updateFundingProjectWithTranslations(
              {
                id: id,
                project: {
                  holderId: updateData.holderId,
                  typeId: updateData.typeId,
                  fciId: updateData.fciId,
                  synchroId: updateData.synchroId,
                  obtainingYear: updateData.obtainingYear,
                  endDate: updateData.endDate,
                  modifiedBy: updateData.modifiedBy,
                },
                translations: updateData.translations
                  ? updateData.translations
                      .filter((t) => t.name && t.name.trim() !== '')
                      .map((t) => ({
                        locale: t.locale,
                        name: t.name || 'Unnamed Project',
                        description: t.description,
                      }))
                  : [],
              },
            );

          if (!updated) {
            return yield* Effect.fail(new FundingProjectNotFoundError({ id }));
          }

          return updated;
        });
      };

      const deleteFundingProject = (id: string) => {
        return Effect.gen(function* () {
          const existingProject =
            yield* fundingProjectsRepository.findFundingProjectById(id);
          if (!existingProject) {
            return yield* Effect.fail(new FundingProjectNotFoundError({ id }));
          }
          const result =
            yield* fundingProjectsRepository.deleteFundingProject(id);
          return result.length > 0;
        });
      };

      return {
        getAllFundingProjects,
        getFundingProjectById,
        createFundingProject,
        updateFundingProject,
        deleteFundingProject,
      } as const;
    }),
  },
) {}
